package com.apexdungeons.core;

import com.apexdungeons.DungeonXMinimal;
import com.apexdungeons.schematics.SchematicFile;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages schematic files and operations
 */
public class SchematicManager {
    private final DungeonXMinimal plugin;
    private final List<SchematicFile> schematics;
    
    public SchematicManager(DungeonXMinimal plugin) {
        this.plugin = plugin;
        this.schematics = new ArrayList<>();
        loadSchematics();
    }
    
    /**
     * Load all schematic files from the schematics directory
     */
    public void loadSchematics() {
        schematics.clear();
        
        File schematicsDir = new File(plugin.getDataFolder(), "schematics");
        if (!schematicsDir.exists()) {
            schematicsDir.mkdirs();
            return;
        }
        
        File[] files = schematicsDir.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".schem") || name.toLowerCase().endsWith(".schematic"));
        
        if (files != null) {
            for (File file : files) {
                String name = file.getName().replaceFirst("[.][^.]+$", ""); // Remove extension
                SchematicFile schematicFile = new SchematicFile(name, file);
                schematics.add(schematicFile);
            }
        }
        
        plugin.getLogger().info("Loaded " + schematics.size() + " schematic files");
    }
    
    /**
     * Get all loaded schematics
     */
    public List<SchematicFile> getAllSchematics() {
        return new ArrayList<>(schematics);
    }
    
    /**
     * Get a schematic by name
     */
    public SchematicFile getSchematic(String name) {
        return schematics.stream()
            .filter(s -> s.getName().equalsIgnoreCase(name))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Paste a schematic at the specified location
     */
    public void pasteSchematic(SchematicFile schematic, Location location, Player player) {
        // This would normally paste the schematic using FAWE or WorldEdit
        // For now, just send a message
        player.sendMessage("§aPasted schematic " + schematic.getName() + " at " + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // TODO: Implement actual schematic pasting
        // - Check if FAWE/WorldEdit is available
        // - Load and paste the schematic file
        // - Handle async operations
        // - Provide progress feedback
    }
    
    /**
     * Refresh the schematic list
     */
    public void refreshSchematics() {
        loadSchematics();
    }
    
    /**
     * Get schematics by category
     */
    public List<SchematicFile> getSchematicsByCategory(String category) {
        return schematics.stream()
            .filter(s -> s.getCategory().equalsIgnoreCase(category))
            .collect(java.util.stream.Collectors.toList());
    }
}
