package com.apexdungeons.integration;

import com.apexdungeons.DungeonX;
import io.lumine.mythic.api.MythicApi;
import io.lumine.mythic.api.mobs.MythicMob;
import io.lumine.mythic.bukkit.MythicBukkit;
import io.lumine.mythic.core.mobs.ActiveMob;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;

import java.util.Optional;
import java.util.logging.Level;

/**
 * Enhanced MythicMobs adapter with robust API integration and safe fallback.
 * Provides primary MythicMobs API access with console command fallback.
 */
public class EnhancedMythicMobsAdapter implements MobAdapter {
    
    private final DungeonX plugin;
    private final boolean mythicMobsAvailable;
    private final boolean fallbackEnabled;
    private final VanillaAdapter vanillaFallback;
    
    public EnhancedMythicMobsAdapter(DungeonX plugin) {
        this.plugin = plugin;
        this.mythicMobsAvailable = checkMythicMobsAvailability();
        this.fallbackEnabled = plugin.getConfigManager().isMythicMobsFallbackEnabled();
        this.vanillaFallback = new VanillaAdapter();
        
        if (mythicMobsAvailable) {
            plugin.getLogger().info("Enhanced MythicMobs adapter initialized successfully");
        } else {
            plugin.getLogger().warning("MythicMobs not available, using fallback mode");
        }
    }
    
    /**
     * Check if MythicMobs is available and functional.
     */
    private boolean checkMythicMobsAvailability() {
        try {
            // Check if MythicMobs plugin is loaded
            if (Bukkit.getPluginManager().getPlugin("MythicMobs") == null) {
                return false;
            }
            
            // Test API access
            MythicApi api = MythicBukkit.inst().getAPIHelper();
            if (api == null) {
                return false;
            }
            
            // Test mob manager access
            var mobManager = MythicBukkit.inst().getMobManager();
            if (mobManager == null) {
                return false;
            }
            
            plugin.getLogger().info("MythicMobs API verification successful");
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "MythicMobs API verification failed", e);
            return false;
        }
    }
    
    @Override
    public LivingEntity spawnMob(String mobType, Location location) {
        return spawnMob(mobType, location, 1);
    }
    
    @Override
    public LivingEntity spawnMob(String mobType, Location location, int level) {
        // Try MythicMobs API first
        if (mythicMobsAvailable) {
            LivingEntity entity = spawnMythicMob(mobType, location, level);
            if (entity != null) {
                return entity;
            }
            
            plugin.getLogger().warning("MythicMobs API spawn failed for " + mobType + ", trying fallback");
        }
        
        // Try console command fallback
        if (fallbackEnabled && mythicMobsAvailable) {
            LivingEntity entity = spawnMythicMobViaCommand(mobType, location, level);
            if (entity != null) {
                return entity;
            }
            
            plugin.getLogger().warning("MythicMobs command fallback failed for " + mobType + ", using vanilla");
        }
        
        // Final fallback to vanilla
        return vanillaFallback.spawnMob(mobType, location, level);
    }
    
    /**
     * Spawn mob using MythicMobs API.
     */
    private LivingEntity spawnMythicMob(String mobType, Location location, int level) {
        try {
            var mobManager = MythicBukkit.inst().getMobManager();
            Optional<MythicMob> mythicMob = mobManager.getMythicMob(mobType);
            
            if (mythicMob.isEmpty()) {
                plugin.getLogger().fine("MythicMob type not found: " + mobType);
                return null;
            }
            
            // Spawn the mob
            ActiveMob activeMob = mythicMob.get().spawn(location, level);
            if (activeMob != null && activeMob.getEntity() != null) {
                LivingEntity entity = activeMob.getEntity().getBukkitEntity();
                
                // Tag entity for tracking
                tagMythicMob(entity, mobType, level);
                
                plugin.getLogger().fine("Successfully spawned MythicMob: " + mobType + " at level " + level);
                return entity;
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to spawn MythicMob via API: " + mobType, e);
        }
        
        return null;
    }
    
    /**
     * Spawn mob using console command fallback.
     */
    private LivingEntity spawnMythicMobViaCommand(String mobType, Location location, int level) {
        try {
            // Store entities before spawn to identify the new one
            var entitiesBefore = location.getWorld().getEntities();
            
            // Execute spawn command
            String command = String.format("mythicmobs mob spawn %s %s %f,%f,%f %d",
                mobType,
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ(),
                level);
            
            boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
            
            if (success) {
                // Find the newly spawned entity
                var entitiesAfter = location.getWorld().getEntities();
                for (var entity : entitiesAfter) {
                    if (!entitiesBefore.contains(entity) && entity instanceof LivingEntity) {
                        LivingEntity livingEntity = (LivingEntity) entity;
                        
                        // Verify it's close to spawn location
                        if (livingEntity.getLocation().distance(location) < 5.0) {
                            tagMythicMob(livingEntity, mobType, level);
                            plugin.getLogger().fine("Successfully spawned MythicMob via command: " + mobType);
                            return livingEntity;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to spawn MythicMob via command: " + mobType, e);
        }
        
        return null;
    }
    
    /**
     * Tag a MythicMob entity for tracking.
     */
    private void tagMythicMob(LivingEntity entity, String mobType, int level) {
        // Add persistent data tags for tracking
        entity.getPersistentDataContainer().set(
            plugin.getKey("mythic_mob_type"),
            org.bukkit.persistence.PersistentDataType.STRING,
            mobType
        );
        
        entity.getPersistentDataContainer().set(
            plugin.getKey("mythic_mob_level"),
            org.bukkit.persistence.PersistentDataType.INTEGER,
            level
        );
        
        entity.getPersistentDataContainer().set(
            plugin.getKey("dungeonx_mob"),
            org.bukkit.persistence.PersistentDataType.BYTE,
            (byte) 1
        );
    }
    
    @Override
    public boolean isMythicMob(LivingEntity entity) {
        if (!mythicMobsAvailable) {
            return false;
        }
        
        try {
            var mobManager = MythicBukkit.inst().getMobManager();
            return mobManager.isActiveMob(entity.getUniqueId());
        } catch (Exception e) {
            // Fallback to persistent data check
            return entity.getPersistentDataContainer().has(
                plugin.getKey("mythic_mob_type"),
                org.bukkit.persistence.PersistentDataType.STRING
            );
        }
    }
    
    @Override
    public String getMobType(LivingEntity entity) {
        if (isMythicMob(entity)) {
            try {
                var mobManager = MythicBukkit.inst().getMobManager();
                var activeMob = mobManager.getActiveMob(entity.getUniqueId());
                if (activeMob.isPresent()) {
                    return activeMob.get().getType().getInternalName();
                }
            } catch (Exception e) {
                // Fallback to persistent data
                return entity.getPersistentDataContainer().get(
                    plugin.getKey("mythic_mob_type"),
                    org.bukkit.persistence.PersistentDataType.STRING
                );
            }
        }
        
        return entity.getType().name();
    }
    
    @Override
    public int getMobLevel(LivingEntity entity) {
        if (isMythicMob(entity)) {
            try {
                var mobManager = MythicBukkit.inst().getMobManager();
                var activeMob = mobManager.getActiveMob(entity.getUniqueId());
                if (activeMob.isPresent()) {
                    return (int) activeMob.get().getLevel();
                }
            } catch (Exception e) {
                // Fallback to persistent data
                Integer level = entity.getPersistentDataContainer().get(
                    plugin.getKey("mythic_mob_level"),
                    org.bukkit.persistence.PersistentDataType.INTEGER
                );
                return level != null ? level : 1;
            }
        }
        
        return 1;
    }
    
    @Override
    public boolean canSpawnMob(String mobType) {
        if (!mythicMobsAvailable) {
            return vanillaFallback.canSpawnMob(mobType);
        }
        
        try {
            var mobManager = MythicBukkit.inst().getMobManager();
            return mobManager.getMythicMob(mobType).isPresent() || vanillaFallback.canSpawnMob(mobType);
        } catch (Exception e) {
            return vanillaFallback.canSpawnMob(mobType);
        }
    }
    
    /**
     * Execute a MythicMobs skill on an entity.
     */
    public boolean executeSkill(String skillName, LivingEntity caster, LivingEntity target) {
        if (!mythicMobsAvailable) {
            return false;
        }
        
        try {
            var skillManager = MythicBukkit.inst().getSkillManager();
            var skill = skillManager.getSkill(skillName);
            
            if (skill.isPresent()) {
                var mobManager = MythicBukkit.inst().getMobManager();
                var activeMob = mobManager.getActiveMob(caster.getUniqueId());
                
                if (activeMob.isPresent()) {
                    // Execute skill with target
                    skill.get().execute(activeMob.get().getEntity(), target.getLocation());
                    return true;
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to execute MythicMobs skill: " + skillName, e);
        }
        
        return false;
    }
    
    /**
     * Get available MythicMobs types.
     */
    public java.util.Set<String> getAvailableMobTypes() {
        java.util.Set<String> types = new java.util.HashSet<>();
        
        if (mythicMobsAvailable) {
            try {
                var mobManager = MythicBukkit.inst().getMobManager();
                for (MythicMob mob : mobManager.getMobTypes()) {
                    types.add(mob.getInternalName());
                }
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Failed to get MythicMobs types", e);
            }
        }
        
        // Add vanilla types
        for (EntityType type : EntityType.values()) {
            if (type.isAlive() && type != EntityType.PLAYER) {
                types.add(type.name());
            }
        }
        
        return types;
    }
    
    /**
     * Check if MythicMobs is available.
     */
    public boolean isMythicMobsAvailable() {
        return mythicMobsAvailable;
    }
    
    /**
     * Get adapter statistics.
     */
    public java.util.Map<String, Object> getAdapterStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("mythicMobsAvailable", mythicMobsAvailable);
        stats.put("fallbackEnabled", fallbackEnabled);
        stats.put("availableMobTypes", getAvailableMobTypes().size());
        return stats;
    }
}
