package com.apexdungeons.core;

import com.apexdungeons.DungeonXMinimal;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.*;
import java.nio.file.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Atomic data persistence system with corruption recovery.
 */
public class DataStore {
    
    private final DungeonXMinimal plugin;
    private final Path dataDirectory;
    private final Gson gson;
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    
    public DataStore(DungeonXMinimal plugin) {
        this.plugin = plugin;
        this.dataDirectory = plugin.getDataFolder().toPath().resolve("data");
        this.gson = new GsonBuilder().setPrettyPrinting().create();
    }
    
    public void initialize() {
        try {
            Files.createDirectories(dataDirectory);
            plugin.getLogger().info("DataStore initialized");
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to initialize DataStore: " + e.getMessage());
        }
    }
    
    public void saveAll() {
        plugin.getLogger().info("Saving all data...");
    }
    
    public void forceSave() {
        saveAll();
    }
    
    public String getStats() {
        return "Cache entries: " + cache.size();
    }
}
