package com.apexdungeons.gui;

import com.apexdungeons.DungeonXMinimal;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * Main Tools GUI - central hub for all dungeon building and management tools.
 * Provides access to template management, building tools, and schematic library.
 */
public class ToolsGUI implements Listener {
    
    private final DungeonXMinimal plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    
    public ToolsGUI(DungeonXMinimal plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the tools GUI for a player.
     */
    public void openToolsGUI(Player player) {
        if (!player.hasPermission("dungeonx.admin")) {
            player.sendMessage("§cYou don't have permission to access DungeonX tools.");
            return;
        }
        
        Inventory gui = createToolsInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }
    
    /**
     * Create the tools inventory.
     */
    private Inventory createToolsInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lDungeonX Tools");
        
        // Template Management Section (Top Row)
        gui.setItem(10, createItem(Material.WRITABLE_BOOK, "§e§lTemplate Manager", 
            "§7Create, edit, and manage", "§7dungeon templates", "", "§aClick to open"));
        
        gui.setItem(11, createItem(Material.EMERALD, "§a§lPublished Templates", 
            "§7View and manage published", "§7templates ready for play", "", "§aClick to open"));
        
        gui.setItem(12, createItem(Material.PAPER, "§f§lDraft Templates", 
            "§7Work on unpublished", "§7template drafts", "", "§aClick to open"));
        
        // Building Tools Section (Middle Row)
        gui.setItem(19, createItem(Material.GOLDEN_PICKAXE, "§6§lSpawner Tool", 
            "§7Place and configure", "§7mob spawners", "", "§aClick to get tool"));
        
        gui.setItem(20, createItem(Material.DIAMOND_SWORD, "§c§lBoss Tool", 
            "§7Place and configure", "§7boss encounters", "", "§aClick to get tool"));
        
        gui.setItem(21, createItem(Material.CHEST, "§e§lChest Tool", 
            "§7Place and configure", "§7loot chests", "", "§aClick to get tool"));
        
        gui.setItem(22, createItem(Material.COMPASS, "§b§lLocation Tool", 
            "§7Set spawn and exit", "§7locations", "", "§aClick to get tool"));
        
        // Configuration Section (Middle Row)
        gui.setItem(13, createItem(Material.CHEST, "§e§lLoot Configuration", 
            "§7Configure loot tables", "§7and treasure drops", "", "§aClick to configure"));
        
        gui.setItem(14, createItem(Material.SPAWNER, "§6§lMob Configuration", 
            "§7Configure mob spawn pools", "§7and creature settings", "", "§aClick to configure"));
        
        gui.setItem(15, createItem(Material.WITHER_SKELETON_SKULL, "§c§lBoss Configuration", 
            "§7Configure boss definitions", "§7and boss encounters", "", "§aClick to configure"));
        
        // Schematic Section (Bottom Row)
        gui.setItem(28, createItem(Material.STRUCTURE_BLOCK, "§d§lSchematic Library", 
            "§7Browse and paste", "§7schematic files", "", "§aClick to open"));
        
        gui.setItem(29, createItem(Material.STRUCTURE_VOID, "§5§l3D Preview", 
            "§7Preview schematics with", "§73D ghost blocks", "", "§aClick to toggle"));
        
        gui.setItem(30, createItem(Material.REDSTONE_BLOCK, "§c§lProcedural Generator", 
            "§7Generate dungeons using", "§7room connectors", "", "§aClick to open"));
        
        // Instance Management Section (Right Side)
        gui.setItem(16, createItem(Material.ENDER_PEARL, "§9§lActive Instances", 
            "§7View and manage", "§7running instances", "", "§7Instances: §e" + plugin.getInstanceManager().getInstanceCount()));
        
        gui.setItem(25, createItem(Material.BARRIER, "§c§lCleanup Tools", 
            "§7Force cleanup and", "§7maintenance tasks", "", "§aClick to open"));
        
        // Statistics and Info (Bottom Right)
        gui.setItem(34, createItem(Material.BOOK, "§a§lStatistics", 
            "§7View system statistics", "§7and performance data", "", "§aClick to view"));
        
        gui.setItem(43, createItem(Material.REDSTONE_TORCH, "§e§lSystem Status", 
            "§7Check integrations and", "§7system health", "", "§aClick to check"));
        
        // Close button
        gui.setItem(49, createItem(Material.RED_STAINED_GLASS_PANE, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots with glass panes
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 0.5f, 1.0f);
        }
        
        handleToolsClick(player, event.getSlot(), clickedItem);
    }
    
    /**
     * Handle clicks in the tools GUI.
     */
    private void handleToolsClick(Player player, int slot, ItemStack item) {
        switch (slot) {
            case 10: // Template Manager
                new TemplateManagerGUI(plugin).openTemplateManager(player);
                break;
                
            case 11: // Published Templates
                new TemplateManagerGUI(plugin).openPublishedTemplates(player);
                break;
                
            case 12: // Draft Templates
                new TemplateManagerGUI(plugin).openDraftTemplates(player);
                break;
                
            case 13: // Loot Configuration
                new LootConfigGUI(plugin).openLootConfig(player);
                break;
                
            case 14: // Mob Configuration
                new MobConfigGUI(plugin).openMobConfig(player);
                break;
                
            case 15: // Boss Configuration
                new BossConfigGUI(plugin).openBossConfig(player);
                break;
                
            case 19: // Spawner Tool
                giveSpawnerTool(player);
                break;
                
            case 20: // Boss Tool
                giveBossTool(player);
                break;
                
            case 21: // Chest Tool
                giveChestTool(player);
                break;
                
            case 22: // Location Tool
                giveLocationTool(player);
                break;
                
            case 28: // Schematic Library
                new SchematicLibraryGUI(plugin).openSchematicLibrary(player);
                break;
                
            case 29: // 3D Preview Toggle
                toggle3DPreview(player);
                break;
                
            case 30: // Procedural Generator
                new ProceduralGeneratorGUI(plugin).openGenerator(player);
                break;
                
            case 16: // Active Instances
                new InstanceManagerGUI(plugin).openInstanceManager(player);
                break;
                
            case 25: // Cleanup Tools
                new CleanupToolsGUI(plugin).openCleanupTools(player);
                break;
                
            case 34: // Statistics
                new StatisticsGUI(plugin).openStatistics(player);
                break;
                
            case 43: // System Status
                new SystemStatusGUI(plugin).openSystemStatus(player);
                break;
                
            case 49: // Close
                player.closeInventory();
                break;
        }
    }
    
    /**
     * Give spawner tool to player.
     */
    private void giveSpawnerTool(Player player) {
        ItemStack tool = createItem(Material.BLAZE_ROD, "§6§lSpawner Tool", 
            "§7Right-click to place spawner", "§7Left-click to edit spawner", 
            "§7Shift-click to remove spawner", "", "§eDungeonX Tool");
        
        player.getInventory().addItem(tool);
        player.sendMessage("§aSpawner tool added to your inventory!");
        player.closeInventory();
    }
    
    /**
     * Give boss tool to player.
     */
    private void giveBossTool(Player player) {
        ItemStack tool = createItem(Material.WITHER_SKELETON_SKULL, "§c§lBoss Tool", 
            "§7Right-click to place boss", "§7Left-click to edit boss", 
            "§7Shift-click to remove boss", "", "§eDungeonX Tool");
        
        player.getInventory().addItem(tool);
        player.sendMessage("§aBoss tool added to your inventory!");
        player.closeInventory();
    }
    
    /**
     * Give chest tool to player.
     */
    private void giveChestTool(Player player) {
        ItemStack tool = createItem(Material.ENDER_CHEST, "§e§lChest Tool", 
            "§7Right-click to place chest", "§7Left-click to edit chest", 
            "§7Shift-click to remove chest", "", "§eDungeonX Tool");
        
        player.getInventory().addItem(tool);
        player.sendMessage("§aChest tool added to your inventory!");
        player.closeInventory();
    }
    
    /**
     * Give location tool to player.
     */
    private void giveLocationTool(Player player) {
        ItemStack tool = createItem(Material.RECOVERY_COMPASS, "§b§lLocation Tool", 
            "§7Right-click to set spawn", "§7Left-click to set exit", 
            "§7Shift-click to clear location", "", "§eDungeonX Tool");
        
        player.getInventory().addItem(tool);
        player.sendMessage("§aLocation tool added to your inventory!");
        player.closeInventory();
    }
    
    /**
     * Toggle 3D preview mode for player.
     */
    private void toggle3DPreview(Player player) {
        // This would toggle a player's 3D preview mode
        // For now, just send a message
        player.sendMessage("§a3D Preview mode toggled!");
        player.closeInventory();
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
        }
    }
    
    /**
     * Check if a player has the tools GUI open.
     */
    public boolean hasToolsGUIOpen(Player player) {
        return openGuis.containsKey(player.getUniqueId());
    }
    
    /**
     * Close tools GUI for a player.
     */
    public void closeToolsGUI(Player player) {
        if (openGuis.containsKey(player.getUniqueId())) {
            player.closeInventory();
        }
    }
}
