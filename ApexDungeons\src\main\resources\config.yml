# ------------------------------------------------------------------------------
# DungeonX Configuration
# Commercial-quality dungeon system for Paper 1.21+
# Features true instancing, reliable saving, GUI-driven workflow, and professional-grade tools.
# ------------------------------------------------------------------------------

# Debug and logging settings
debug:
  enabled: false
  verbose-logging: false

# Datastore settings for atomic persistence
datastore:
  # Autosave interval in seconds (default: 5 minutes)
  autosave-interval: 300
  # Datastore filename
  filename: "datastore.yml"

# Instance management settings
instances:
  # Prefix for instance world names
  world-prefix: "dungeonx_instance_"
  # Y level where template structures are pasted
  paste-y-level: 64
  # Maximum age of instances before cleanup (hours)
  max-age-hours: 24
  # Clean up stray instance worlds on server startup
  cleanup-on-boot: true

# Performance settings
performance:
  # Maximum concurrent instances
  max-concurrent-instances: 10
  # Maximum players per instance
  max-players-per-instance: 8
  # Cleanup task interval in seconds
  cleanup-task-interval: 30

# Integration settings
integrations:
  mythicmobs:
    enabled: true
    allow-fallback: true
  fawe:
    enabled: true
    async-paste: true
  placeholderapi:
    enabled: true
  vault:
    enabled: true

# Visual effects settings
effects:
  enabled: true
  titles:
    enabled: true
  sounds:
    enabled: true
  particles:
    enabled: true
  boss-bars:
    enabled: true

# GUI settings
gui:
  title: "DungeonX"
  sounds:
    enabled: true
  animations:
    enabled: true

# Schematic settings
schematics:
  folder: "schematics"
  preview:
    enabled: true
    duration: 30

# Default template settings
templates:
  defaults:
    max-players: 4
    difficulty: "normal"
    time-limit: 0
    allow-respawn: true
    keep-inventory: false