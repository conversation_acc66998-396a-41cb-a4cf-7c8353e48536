package com.apexdungeons.integration;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Vanilla Minecraft mob adapter
 */
public class VanillaAdapter implements MobAdapter {
    
    private boolean enabled = true;
    
    private static final List<String> VANILLA_MOBS = Arrays.asList(
        "ZOMBI<PERSON>", "SKELET<PERSON>", "CREEPER", "SPIDER", "ENDERMAN", "WITCH",
        "BLAZE", "GHAST", "MAGMA_CUBE", "SLIME", "SILVERFISH", "CAVE_SPIDER",
        "WITH<PERSON>_SKELETON", "<PERSON>OMBIE_PIGMAN", "PIGLIN", "PIGLIN_BRUTE",
        "HOGLIN", "ZOGLIN", "STRIDER", "ZOM<PERSON><PERSON>ED_PIGLIN", "<PERSON>HA<PERSON><PERSON>",
        "DROWNED", "<PERSON>US<PERSON>", "STRAY", "V<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ER_G<PERSON>AR<PERSON>AN", "SHULKER",
        "ENDERMITE", "POLAR_BEAR", "LLAMA", "WOLF", "OCELOT", "PARROT",
        "BAT", "SQUID", "DOLPHIN", "COD", "SALMON", "PUFFERFISH",
        "TROPICAL_FISH", "TURTLE", "CHICKEN", "COW", "PIG", "SHEEP",
        "HORSE", "DONKEY", "MULE", "RABBIT", "VILLAGER", "IRON_GOLEM",
        "SNOW_GOLEM", "WITHER", "ENDER_DRAGON"
    );
    
    @Override
    public Entity spawnMob(String mobType, Location location) {
        try {
            EntityType entityType = EntityType.valueOf(mobType.toUpperCase());
            return location.getWorld().spawnEntity(location, entityType);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    @Override
    public boolean isValidMobType(String mobType) {
        try {
            EntityType.valueOf(mobType.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    @Override
    public List<String> getAvailableMobTypes() {
        return new ArrayList<>(VANILLA_MOBS);
    }
    
    @Override
    public String getMobDisplayName(String mobType) {
        return mobType.toLowerCase().replace("_", " ");
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public String getAdapterName() {
        return "Vanilla";
    }
    
    @Override
    public void initialize() {
        enabled = true;
    }
    
    @Override
    public void shutdown() {
        enabled = false;
    }
}
