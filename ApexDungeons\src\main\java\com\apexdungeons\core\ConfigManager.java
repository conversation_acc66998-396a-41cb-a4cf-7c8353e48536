package com.apexdungeons.core;

import com.apexdungeons.DungeonXMinimal;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages configuration files and settings for DungeonX.
 * Provides centralized access to all configuration values with defaults.
 */
public class ConfigManager {
    
    private final DungeonXMinimal plugin;
    private final Map<String, FileConfiguration> configs = new HashMap<>();
    
    // Configuration file names
    private static final String MAIN_CONFIG = "config.yml";
    private static final String MESSAGES_CONFIG = "messages.yml";
    private static final String LOOT_CONFIG = "loot.yml";
    private static final String MOBS_CONFIG = "mobs.yml";
    private static final String BOSSES_CONFIG = "bosses.yml";
    
    public ConfigManager(DungeonXMinimal plugin) {
        this.plugin = plugin;
        loadConfigurations();
    }
    
    /**
     * Load all configuration files.
     */
    public void loadConfigurations() {
        // Load main config
        plugin.saveDefaultConfig();
        configs.put(MAIN_CONFIG, plugin.getConfig());
        
        // Load additional configs
        loadConfig(MESSAGES_CONFIG);
        loadConfig(LOOT_CONFIG);
        loadConfig(MOBS_CONFIG);
        loadConfig(BOSSES_CONFIG);
        
        plugin.getLogger().info("Loaded " + configs.size() + " configuration files");
    }
    
    /**
     * Load a specific configuration file.
     */
    private void loadConfig(String fileName) {
        File configFile = new File(plugin.getDataFolder(), fileName);
        
        // Save default if doesn't exist
        if (!configFile.exists()) {
            plugin.saveResource(fileName, false);
        }
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);
        configs.put(fileName, config);
    }
    
    /**
     * Get a configuration by name.
     */
    public FileConfiguration getConfig(String configName) {
        return configs.get(configName);
    }
    
    /**
     * Get the main configuration.
     */
    public FileConfiguration getMainConfig() {
        return configs.get(MAIN_CONFIG);
    }
    
    /**
     * Get the messages configuration.
     */
    public FileConfiguration getMessagesConfig() {
        return configs.get(MESSAGES_CONFIG);
    }
    
    /**
     * Get the loot configuration.
     */
    public FileConfiguration getLootConfig() {
        return configs.get(LOOT_CONFIG);
    }
    
    /**
     * Get the mobs configuration.
     */
    public FileConfiguration getMobsConfig() {
        return configs.get(MOBS_CONFIG);
    }
    
    /**
     * Get the bosses configuration.
     */
    public FileConfiguration getBossesConfig() {
        return configs.get(BOSSES_CONFIG);
    }
    
    /**
     * Reload all configurations.
     */
    public void reloadConfigurations() {
        plugin.reloadConfig();
        configs.clear();
        loadConfigurations();
        plugin.getLogger().info("Reloaded all configurations");
    }
    
    /**
     * Save a configuration file.
     */
    public void saveConfig(String configName) {
        FileConfiguration config = configs.get(configName);
        if (config != null) {
            try {
                File configFile = new File(plugin.getDataFolder(), configName);
                config.save(configFile);
            } catch (IOException e) {
                plugin.getLogger().severe("Failed to save config " + configName + ": " + e.getMessage());
            }
        }
    }
    
    // Configuration value getters with defaults
    
    /**
     * Get datastore settings.
     */
    public long getAutosaveInterval() {
        return getMainConfig().getLong("datastore.autosave-interval", 300); // 5 minutes
    }
    
    public String getDatastoreFile() {
        return getMainConfig().getString("datastore.filename", "datastore.yml");
    }
    
    /**
     * Get instance settings.
     */
    public String getInstanceWorldPrefix() {
        return getMainConfig().getString("instances.world-prefix", "dungeonx_instance_");
    }
    
    public int getInstancePasteYLevel() {
        return getMainConfig().getInt("instances.paste-y-level", 64);
    }
    
    public long getInstanceMaxAgeHours() {
        return getMainConfig().getLong("instances.max-age-hours", 24);
    }
    
    public boolean getInstanceCleanupOnBoot() {
        return getMainConfig().getBoolean("instances.cleanup-on-boot", true);
    }
    
    /**
     * Get integration settings.
     */
    public boolean isMythicMobsEnabled() {
        return getMainConfig().getBoolean("integrations.mythicmobs.enabled", true);
    }
    
    public boolean isMythicMobsFallbackEnabled() {
        return getMainConfig().getBoolean("integrations.mythicmobs.allow-fallback", true);
    }
    
    public boolean isFaweEnabled() {
        return getMainConfig().getBoolean("integrations.fawe.enabled", true);
    }
    
    public boolean isFaweAsyncPaste() {
        return getMainConfig().getBoolean("integrations.fawe.async-paste", true);
    }
    
    public boolean isPlaceholderApiEnabled() {
        return getMainConfig().getBoolean("integrations.placeholderapi.enabled", true);
    }
    
    public boolean isVaultEnabled() {
        return getMainConfig().getBoolean("integrations.vault.enabled", true);
    }
    
    /**
     * Get effect settings.
     */
    public boolean areEffectsEnabled() {
        return getMainConfig().getBoolean("effects.enabled", true);
    }
    
    public boolean areTitlesEnabled() {
        return getMainConfig().getBoolean("effects.titles.enabled", true);
    }
    
    public boolean areSoundsEnabled() {
        return getMainConfig().getBoolean("effects.sounds.enabled", true);
    }
    
    public boolean areParticlesEnabled() {
        return getMainConfig().getBoolean("effects.particles.enabled", true);
    }
    
    public boolean areBossBarsEnabled() {
        return getMainConfig().getBoolean("effects.boss-bars.enabled", true);
    }
    
    /**
     * Get performance settings.
     */
    public int getMaxConcurrentInstances() {
        return getMainConfig().getInt("performance.max-concurrent-instances", 10);
    }
    
    public int getMaxPlayersPerInstance() {
        return getMainConfig().getInt("performance.max-players-per-instance", 8);
    }
    
    public long getCleanupTaskInterval() {
        return getMainConfig().getLong("performance.cleanup-task-interval", 30); // seconds
    }
    
    /**
     * Get GUI settings.
     */
    public String getGuiTitle() {
        return getMainConfig().getString("gui.title", "DungeonX");
    }
    
    public boolean isGuiSoundsEnabled() {
        return getMainConfig().getBoolean("gui.sounds.enabled", true);
    }
    
    public boolean isGuiAnimationsEnabled() {
        return getMainConfig().getBoolean("gui.animations.enabled", true);
    }
    
    /**
     * Get message from messages config.
     */
    public String getMessage(String key) {
        return getMessagesConfig().getString(key, "Missing message: " + key);
    }
    
    public String getMessage(String key, String defaultValue) {
        return getMessagesConfig().getString(key, defaultValue);
    }
    
    /**
     * Get formatted message with placeholders.
     */
    public String getFormattedMessage(String key, Map<String, String> placeholders) {
        String message = getMessage(key);
        
        if (placeholders != null) {
            for (Map.Entry<String, String> entry : placeholders.entrySet()) {
                message = message.replace("{" + entry.getKey() + "}", entry.getValue());
            }
        }
        
        return message;
    }
    
    /**
     * Check if debug mode is enabled.
     */
    public boolean isDebugEnabled() {
        return getMainConfig().getBoolean("debug.enabled", false);
    }
    
    public boolean isDebugMode() {
        return isDebugEnabled();
    }
    
    public boolean isVerboseLogging() {
        return getMainConfig().getBoolean("debug.verbose-logging", false);
    }
    
    /**
     * Get schematic settings.
     */
    public String getSchematicsFolder() {
        return getMainConfig().getString("schematics.folder", "schematics");
    }
    
    public boolean isSchematicPreviewEnabled() {
        return getMainConfig().getBoolean("schematics.preview.enabled", true);
    }
    
    public int getSchematicPreviewDuration() {
        return getMainConfig().getInt("schematics.preview.duration", 30); // seconds
    }
    
    /**
     * Get default template settings.
     */
    public Map<String, Object> getDefaultTemplateSettings() {
        Map<String, Object> defaults = new HashMap<>();
        defaults.put("maxPlayers", getMainConfig().getInt("templates.defaults.max-players", 4));
        defaults.put("difficulty", getMainConfig().getString("templates.defaults.difficulty", "normal"));
        defaults.put("timeLimit", getMainConfig().getInt("templates.defaults.time-limit", 0));
        defaults.put("allowRespawn", getMainConfig().getBoolean("templates.defaults.allow-respawn", true));
        defaults.put("keepInventory", getMainConfig().getBoolean("templates.defaults.keep-inventory", false));
        return defaults;
    }
    
    /**
     * Get statistics about loaded configurations.
     */
    public Map<String, Object> getConfigStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("loadedConfigs", configs.size());
        stats.put("configNames", configs.keySet());
        stats.put("autosaveInterval", getAutosaveInterval());
        stats.put("maxInstances", getMaxConcurrentInstances());
        stats.put("debugEnabled", isDebugEnabled());
        return stats;
    }
}
