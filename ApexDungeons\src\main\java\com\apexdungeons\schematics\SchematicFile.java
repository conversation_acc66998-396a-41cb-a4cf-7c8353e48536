package com.apexdungeons.schematics;

import java.io.File;
import java.util.List;
import java.util.ArrayList;

/**
 * Represents a schematic file with metadata
 */
public class SchematicFile {
    private String name;
    private String fileName;
    private File file;
    private String description;
    private List<String> tags;
    private int width;
    private int height;
    private int length;
    private boolean enabled;
    private String category;
    private int blockCount;
    
    public SchematicFile(String name, File file) {
        this.name = name;
        this.fileName = file.getName();
        this.file = file;
        this.description = "";
        this.tags = new ArrayList<>();
        this.width = 0;
        this.height = 0;
        this.length = 0;
        this.enabled = true;
        this.category = "general";
        this.blockCount = 0;
    }
    
    // Getters and setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public File getFile() { return file; }
    public void setFile(File file) { this.file = file; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }
    
    public int getWidth() { return width; }
    public void setWidth(int width) { this.width = width; }
    
    public int getHeight() { return height; }
    public void setHeight(int height) { this.height = height; }
    
    public int getLength() { return length; }
    public void setLength(int length) { this.length = length; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public int getBlockCount() { return blockCount; }
    public void setBlockCount(int blockCount) { this.blockCount = blockCount; }
    
    public void addTag(String tag) {
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }
    
    public void removeTag(String tag) {
        this.tags.remove(tag);
    }
    
    public boolean hasTag(String tag) {
        return this.tags.contains(tag);
    }
    
    public boolean exists() {
        return file != null && file.exists();
    }
    
    public long getFileSize() {
        return file != null ? file.length() : 0;
    }
    
    public String getFormattedSize() {
        long size = getFileSize();
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return (size / 1024) + " KB";
        return (size / (1024 * 1024)) + " MB";
    }
    
    @Override
    public String toString() {
        return name + " (" + fileName + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SchematicFile that = (SchematicFile) obj;
        return fileName.equals(that.fileName);
    }
    
    @Override
    public int hashCode() {
        return fileName.hashCode();
    }
}
