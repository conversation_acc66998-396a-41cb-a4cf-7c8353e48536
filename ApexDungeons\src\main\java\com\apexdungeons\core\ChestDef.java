package com.apexdungeons.core;

import org.bukkit.Location;
import org.bukkit.Material;
import java.util.List;
import java.util.ArrayList;

/**
 * Represents a chest definition with loot table configuration
 */
public class ChestDef {
    private String id;
    private Location location;
    private List<String> lootTables;
    private String chestType;
    private boolean locked;
    private String keyItem;
    private int respawnTime; // in minutes
    private boolean enabled;
    private String displayName;
    private List<String> lore;
    
    public ChestDef(String id) {
        this.id = id;
        this.lootTables = new ArrayList<>();
        this.chestType = "CHEST";
        this.locked = false;
        this.keyItem = "";
        this.respawnTime = 30;
        this.enabled = true;
        this.displayName = "Dungeon Chest";
        this.lore = new ArrayList<>();
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }
    
    public List<String> getLootTables() { return lootTables; }
    public void setLootTables(List<String> lootTables) { this.lootTables = lootTables; }
    
    public String getChestType() { return chestType; }
    public void setChestType(String chestType) { this.chestType = chestType; }
    
    public boolean isLocked() { return locked; }
    public void setLocked(boolean locked) { this.locked = locked; }
    
    public String getKeyItem() { return keyItem; }
    public void setKeyItem(String keyItem) { this.keyItem = keyItem; }
    
    public int getRespawnTime() { return respawnTime; }
    public void setRespawnTime(int respawnTime) { this.respawnTime = respawnTime; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public List<String> getLore() { return lore; }
    public void setLore(List<String> lore) { this.lore = lore; }
    
    public void addLootTable(String lootTable) {
        this.lootTables.add(lootTable);
    }
    
    public void removeLootTable(String lootTable) {
        this.lootTables.remove(lootTable);
    }
    
    public void addLoreLine(String line) {
        this.lore.add(line);
    }
    
    public void removeLoreLine(String line) {
        this.lore.remove(line);
    }
    
    public void clearLore() {
        this.lore.clear();
    }
}
