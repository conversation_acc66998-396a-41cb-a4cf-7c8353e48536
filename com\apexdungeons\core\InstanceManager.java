package com.apexdungeons.core;

import com.apexdungeons.DungeonXMinimal;
import org.bukkit.*;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages dungeon instances - temporary isolated worlds created from templates.
 * Handles instance creation, lifecycle management, and cleanup with crash safety.
 */
public class InstanceManager {
    
    private final DungeonXMinimal plugin;
    private final Map<String, DungeonInstance> activeInstances = new ConcurrentHashMap<>();
    private final Map<UUID, String> playerInstances = new ConcurrentHashMap<>(); // player -> instanceId
    
    // Configuration
    private final String instancePrefix;
    private final int pasteYLevel;
    private final long maxInstanceAge; // Maximum age before cleanup (milliseconds)
    
    public InstanceManager(DungeonXMinimal plugin) {
        this.plugin = plugin;
        this.instancePrefix = plugin.getConfig().getString("instances.world-prefix", "dungeonx_instance_");
        this.pasteYLevel = plugin.getConfig().getInt("instances.paste-y-level", 64);
        this.maxInstanceAge = plugin.getConfig().getLong("instances.max-age-hours", 24) * 60 * 60 * 1000L;
        
        // Clean up any stray instance worlds from previous crashes
        cleanupStrayWorlds();
    }
    
    /**
     * Create a new instance from a published template.
     */
    public CompletableFuture<DungeonInstance> createInstance(String templateId, List<Player> players) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Get template
                DungeonTemplate template = plugin.getTemplateManager().getTemplate(templateId);
                if (template == null) {
                    throw new IllegalArgumentException("Template not found: " + templateId);
                }
                
                if (!template.isPublished()) {
                    throw new IllegalArgumentException("Template is not published: " + templateId);
                }
                
                // Generate unique instance ID
                String instanceId = generateInstanceId(templateId);
                
                // Create instance world
                World instanceWorld = createInstanceWorld(instanceId, template);
                if (instanceWorld == null) {
                    throw new RuntimeException("Failed to create instance world");
                }
                
                // Create instance object
                DungeonInstance instance = new DungeonInstance(instanceId, template, instanceWorld, players);
                
                // Register instance on main thread
                Bukkit.getScheduler().runTask(plugin, () -> {
                    registerInstance(instance);
                });
                
                plugin.getLogger().info("Created instance " + instanceId + " for " + players.size() + " players");
                return instance;
                
            } catch (Exception e) {
                plugin.getLogger().severe("Failed to create instance: " + e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Generate a unique instance ID.
     */
    private String generateInstanceId(String templateId) {
        return templateId + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * Create an isolated instance world.
     */
    private World createInstanceWorld(String instanceId, DungeonTemplate template) {
        String worldName = instancePrefix + instanceId;
        
        try {
            // Create void world
            WorldCreator creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.type(WorldType.FLAT);
            creator.generatorSettings("minecraft:bedrock,2*minecraft:dirt,minecraft:grass_block;minecraft:plains;village");
            creator.generateStructures(false);
            
            World world = creator.createWorld();
            if (world == null) {
                throw new RuntimeException("Failed to create world: " + worldName);
            }
            
            // Configure world settings
            configureInstanceWorld(world);
            
            // Paste template structure if schematic exists
            if (template.getSchematicFile() != null) {
                pasteTemplateStructure(world, template);
            }
            
            return world;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to create instance world " + worldName + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Configure instance world settings.
     */
    private void configureInstanceWorld(World world) {
        // Disable natural spawning
        world.setSpawnFlags(false, false);
        
        // Set game rules
        world.setGameRule(GameRule.DO_MOB_SPAWNING, false);
        world.setGameRule(GameRule.DO_DAYLIGHT_CYCLE, false);
        world.setGameRule(GameRule.DO_WEATHER_CYCLE, false);
        world.setGameRule(GameRule.KEEP_INVENTORY, true);
        world.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
        world.setGameRule(GameRule.DO_FIRE_TICK, false);
        world.setGameRule(GameRule.MOB_GRIEFING, false);
        
        // Set time and weather
        world.setTime(18000); // Night time for atmosphere
        world.setStorm(false);
        world.setThundering(false);
        
        // Set spawn location
        world.setSpawnLocation(0, pasteYLevel, 0);
        world.setKeepSpawnInMemory(false);
        
        plugin.getLogger().fine("Configured instance world: " + world.getName());
    }
    
    /**
     * Paste template structure into instance world.
     */
    private void pasteTemplateStructure(World world, DungeonTemplate template) {
        // This would integrate with FAWE or fallback to manual pasting
        // For now, just log the intent
        plugin.getLogger().info("Would paste schematic: " + template.getSchematicFile() + " into " + world.getName());
        
        // TODO: Implement schematic pasting with FAWE integration
        // - Check if FAWE is available
        // - Load schematic file
        // - Paste at configured Y level
        // - Handle async pasting with callback
    }
    
    /**
     * Register an active instance.
     */
    private void registerInstance(DungeonInstance instance) {
        activeInstances.put(instance.getInstanceId(), instance);
        
        // Map players to instance
        for (Player player : instance.getPlayers()) {
            playerInstances.put(player.getUniqueId(), instance.getInstanceId());
        }
        
        plugin.getLogger().info("Registered instance: " + instance.getInstanceId());
    }
    
    /**
     * Get instance by ID.
     */
    public DungeonInstance getInstance(String instanceId) {
        return activeInstances.get(instanceId);
    }
    
    /**
     * Get instance for a player.
     */
    public DungeonInstance getPlayerInstance(Player player) {
        String instanceId = playerInstances.get(player.getUniqueId());
        return instanceId != null ? activeInstances.get(instanceId) : null;
    }
    
    /**
     * Remove a player from their current instance.
     */
    public void removePlayerFromInstance(Player player) {
        String instanceId = playerInstances.remove(player.getUniqueId());
        if (instanceId != null) {
            DungeonInstance instance = activeInstances.get(instanceId);
            if (instance != null) {
                instance.removePlayer(player);
                
                // If instance is empty, mark for cleanup
                if (instance.getPlayers().isEmpty()) {
                    instance.markForCleanup();
                }
            }
        }
    }
    
    /**
     * Complete an instance (success or failure).
     */
    public void completeInstance(String instanceId, boolean success) {
        DungeonInstance instance = activeInstances.get(instanceId);
        if (instance != null) {
            instance.complete(success);
            
            // Return players to main world
            returnPlayersToMainWorld(instance);
            
            // Schedule cleanup
            scheduleInstanceCleanup(instance);
        }
    }
    
    /**
     * Return players to the main world.
     */
    private void returnPlayersToMainWorld(DungeonInstance instance) {
        World mainWorld = Bukkit.getWorlds().get(0); // Default world
        Location spawnLocation = mainWorld.getSpawnLocation();
        
        for (Player player : instance.getPlayers()) {
            player.teleport(spawnLocation);
            playerInstances.remove(player.getUniqueId());
            
            // Send completion message
            if (instance.isCompleted()) {
                player.sendMessage(ChatColor.GREEN + "Dungeon completed successfully!");
            } else {
                player.sendMessage(ChatColor.RED + "Dungeon run ended.");
            }
        }
    }
    
    /**
     * Schedule instance cleanup after a delay.
     */
    private void scheduleInstanceCleanup(DungeonInstance instance) {
        // Clean up after 30 seconds to allow for any final processing
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            cleanupInstance(instance.getInstanceId());
        }, 600L); // 30 seconds
    }
    
    /**
     * Clean up an instance and its world.
     */
    public void cleanupInstance(String instanceId) {
        DungeonInstance instance = activeInstances.remove(instanceId);
        if (instance == null) {
            return;
        }
        
        World instanceWorld = instance.getWorld();
        
        // Remove any remaining players
        for (Player player : instanceWorld.getPlayers()) {
            World mainWorld = Bukkit.getWorlds().get(0);
            player.teleport(mainWorld.getSpawnLocation());
            playerInstances.remove(player.getUniqueId());
        }
        
        // Unload and delete world
        String worldName = instanceWorld.getName();
        if (Bukkit.unloadWorld(instanceWorld, false)) {
            // Delete world folder
            deleteWorldFolder(worldName);
            plugin.getLogger().info("Cleaned up instance: " + instanceId);
        } else {
            plugin.getLogger().warning("Failed to unload instance world: " + worldName);
        }
    }
    
    /**
     * Delete a world folder from disk.
     */
    private void deleteWorldFolder(String worldName) {
        File worldFolder = new File(Bukkit.getWorldContainer(), worldName);
        if (worldFolder.exists()) {
            try {
                deleteDirectory(worldFolder.toPath());
                plugin.getLogger().fine("Deleted world folder: " + worldName);
            } catch (IOException e) {
                plugin.getLogger().warning("Failed to delete world folder " + worldName + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Recursively delete a directory.
     */
    private void deleteDirectory(Path directory) throws IOException {
        Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }
            
            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }
    
    /**
     * Clean up expired instances.
     */
    public void cleanupExpiredInstances() {
        long currentTime = System.currentTimeMillis();
        List<String> toCleanup = new ArrayList<>();
        
        for (DungeonInstance instance : activeInstances.values()) {
            if (instance.isMarkedForCleanup() || 
                (currentTime - instance.getCreatedTime()) > maxInstanceAge) {
                toCleanup.add(instance.getInstanceId());
            }
        }
        
        for (String instanceId : toCleanup) {
            cleanupInstance(instanceId);
        }
        
        if (!toCleanup.isEmpty()) {
            plugin.getLogger().info("Cleaned up " + toCleanup.size() + " expired instances");
        }
    }
    
    /**
     * Clean up stray instance worlds from previous crashes.
     */
    private void cleanupStrayWorlds() {
        File worldContainer = Bukkit.getWorldContainer();
        File[] files = worldContainer.listFiles();
        
        if (files == null) return;
        
        int cleaned = 0;
        for (File file : files) {
            if (file.isDirectory() && file.getName().startsWith(instancePrefix)) {
                try {
                    deleteDirectory(file.toPath());
                    cleaned++;
                } catch (IOException e) {
                    plugin.getLogger().warning("Failed to clean up stray world " + file.getName() + ": " + e.getMessage());
                }
            }
        }
        
        if (cleaned > 0) {
            plugin.getLogger().info("Cleaned up " + cleaned + " stray instance worlds from previous sessions");
        }
    }
    
    /**
     * Get all active instances.
     */
    public Collection<DungeonInstance> getActiveInstances() {
        return new ArrayList<>(activeInstances.values());
    }
    
    /**
     * Get instance count.
     */
    public int getInstanceCount() {
        return activeInstances.size();
    }
    
    /**
     * Shutdown and cleanup all instances.
     */
    public void shutdown() {
        plugin.getLogger().info("Shutting down InstanceManager, cleaning up " + activeInstances.size() + " instances...");
        
        // Clean up all active instances
        for (String instanceId : new ArrayList<>(activeInstances.keySet())) {
            cleanupInstance(instanceId);
        }
        
        activeInstances.clear();
        playerInstances.clear();
        
        plugin.getLogger().info("InstanceManager shutdown complete");
    }
}
