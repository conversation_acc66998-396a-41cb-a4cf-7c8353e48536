package com.apexdungeons.core;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents a runtime dungeon instance - a temporary isolated world where players run dungeons.
 * Each instance is created from a published template and has its own isolated state.
 */
public class DungeonInstance {
    
    public enum InstanceState {
        CREATING,    // Instance is being created
        ACTIVE,      // Instance is running
        COMPLETED,   // Instance completed successfully
        FAILED,      // Instance failed
        CLEANUP      // Instance is being cleaned up
    }
    
    private final String instanceId;
    private final String templateId;
    private final DungeonTemplate template;
    private final World world;
    private final List<Player> players;
    private final long createdTime;
    
    private InstanceState state;
    private boolean markedForCleanup;
    private long startTime;
    private long endTime;
    
    // Runtime component tracking
    private final Map<String, SpawnerDef> activeSpawners = new ConcurrentHashMap<>();
    private final Map<String, BossDef> activeBosses = new ConcurrentHashMap<>();
    private final Map<String, ChestDef> activeChests = new ConcurrentHashMap<>();
    
    // Runtime statistics
    private int mobsKilled = 0;
    private int bossesKilled = 0;
    private int chestsOpened = 0;
    private int playersRevived = 0;
    
    public DungeonInstance(String instanceId, DungeonTemplate template, World world, List<Player> players) {
        this.instanceId = instanceId;
        this.templateId = template.getId();
        this.template = template.copy(); // Create a copy to avoid template modifications
        this.world = world;
        this.players = new ArrayList<>(players);
        this.createdTime = System.currentTimeMillis();
        this.state = InstanceState.CREATING;
        this.markedForCleanup = false;
        
        // Initialize runtime components from template
        initializeComponents();
    }
    
    /**
     * Initialize runtime components from the template.
     */
    private void initializeComponents() {
        // Copy spawners and reset their tracking
        for (SpawnerDef spawner : template.getSpawners()) {
            SpawnerDef runtimeSpawner = SpawnerDef.deserialize(spawner.serialize());
            runtimeSpawner.resetTracking();
            activeSpawners.put(spawner.getId(), runtimeSpawner);
        }
        
        // Copy bosses and reset their tracking
        for (BossDef boss : template.getBosses()) {
            BossDef runtimeBoss = BossDef.deserialize(boss.serialize());
            runtimeBoss.resetTracking();
            activeBosses.put(boss.getId(), runtimeBoss);
        }
        
        // Copy chests and reset their tracking
        for (ChestDef chest : template.getChests()) {
            ChestDef runtimeChest = ChestDef.deserialize(chest.serialize());
            runtimeChest.resetAllTracking();
            activeChests.put(chest.getId(), runtimeChest);
        }
    }
    
    /**
     * Start the instance (players have entered).
     */
    public void start() {
        if (state == InstanceState.CREATING) {
            state = InstanceState.ACTIVE;
            startTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Complete the instance.
     */
    public void complete(boolean success) {
        if (state == InstanceState.ACTIVE) {
            state = success ? InstanceState.COMPLETED : InstanceState.FAILED;
            endTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Mark this instance for cleanup.
     */
    public void markForCleanup() {
        markedForCleanup = true;
        if (state == InstanceState.ACTIVE) {
            state = InstanceState.CLEANUP;
        }
    }
    
    /**
     * Add a player to this instance.
     */
    public void addPlayer(Player player) {
        if (!players.contains(player)) {
            players.add(player);
        }
    }
    
    /**
     * Remove a player from this instance.
     */
    public void removePlayer(Player player) {
        players.remove(player);
    }
    
    /**
     * Check if this instance contains a player.
     */
    public boolean hasPlayer(Player player) {
        return players.contains(player);
    }
    
    /**
     * Get the spawn location for this instance.
     */
    public Location getSpawnLocation() {
        Location templateSpawn = template.getSpawnLocation();
        if (templateSpawn != null) {
            // Translate to instance world
            return new Location(world, templateSpawn.getX(), templateSpawn.getY(), templateSpawn.getZ(),
                templateSpawn.getYaw(), templateSpawn.getPitch());
        }
        // Default spawn
        return new Location(world, 0.5, 64, 0.5);
    }
    
    /**
     * Get the exit location for this instance.
     */
    public Location getExitLocation() {
        Location templateExit = template.getExitLocation();
        if (templateExit != null) {
            // Translate to instance world
            return new Location(world, templateExit.getX(), templateExit.getY(), templateExit.getZ(),
                templateExit.getYaw(), templateExit.getPitch());
        }
        // Default to spawn location
        return getSpawnLocation();
    }
    
    /**
     * Get a spawner by ID.
     */
    public SpawnerDef getSpawner(String spawnerId) {
        return activeSpawners.get(spawnerId);
    }
    
    /**
     * Get a boss by ID.
     */
    public BossDef getBoss(String bossId) {
        return activeBosses.get(bossId);
    }
    
    /**
     * Get a chest by ID.
     */
    public ChestDef getChest(String chestId) {
        return activeChests.get(chestId);
    }
    
    /**
     * Record that a mob was killed.
     */
    public void recordMobKill() {
        mobsKilled++;
    }
    
    /**
     * Record that a boss was killed.
     */
    public void recordBossKill() {
        bossesKilled++;
    }
    
    /**
     * Record that a chest was opened.
     */
    public void recordChestOpen() {
        chestsOpened++;
    }
    
    /**
     * Record that a player was revived.
     */
    public void recordPlayerRevive() {
        playersRevived++;
    }
    
    /**
     * Get the duration of this instance in milliseconds.
     */
    public long getDuration() {
        if (startTime == 0) {
            return 0;
        }
        long endTimeToUse = endTime > 0 ? endTime : System.currentTimeMillis();
        return endTimeToUse - startTime;
    }
    
    /**
     * Get formatted duration string.
     */
    public String getFormattedDuration() {
        long duration = getDuration();
        long minutes = duration / 60000;
        long seconds = (duration % 60000) / 1000;
        return String.format("%d:%02d", minutes, seconds);
    }
    
    /**
     * Check if all objectives are completed.
     */
    public boolean areObjectivesCompleted() {
        // Check if all bosses are defeated
        for (BossDef boss : activeBosses.values()) {
            if (boss.isSpawned() && !boss.isEnraged()) {
                // Boss is alive (this is a simplified check)
                return false;
            }
        }
        
        // Additional objective checks can be added here
        return true;
    }
    
    /**
     * Get instance statistics.
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("instanceId", instanceId);
        stats.put("templateId", templateId);
        stats.put("state", state.name());
        stats.put("playerCount", players.size());
        stats.put("duration", getDuration());
        stats.put("formattedDuration", getFormattedDuration());
        stats.put("mobsKilled", mobsKilled);
        stats.put("bossesKilled", bossesKilled);
        stats.put("chestsOpened", chestsOpened);
        stats.put("playersRevived", playersRevived);
        stats.put("objectivesCompleted", areObjectivesCompleted());
        return stats;
    }
    
    // Getters
    public String getInstanceId() { return instanceId; }
    public String getTemplateId() { return templateId; }
    public DungeonTemplate getTemplate() { return template; }
    public World getWorld() { return world; }
    public List<Player> getPlayers() { return new ArrayList<>(players); }
    public long getCreatedTime() { return createdTime; }
    public InstanceState getState() { return state; }
    public boolean isMarkedForCleanup() { return markedForCleanup; }
    public long getStartTime() { return startTime; }
    public long getEndTime() { return endTime; }
    public Collection<SpawnerDef> getActiveSpawners() { return new ArrayList<>(activeSpawners.values()); }
    public Collection<BossDef> getActiveBosses() { return new ArrayList<>(activeBosses.values()); }
    public Collection<ChestDef> getActiveChests() { return new ArrayList<>(activeChests.values()); }
    public int getMobsKilled() { return mobsKilled; }
    public int getBossesKilled() { return bossesKilled; }
    public int getChestsOpened() { return chestsOpened; }
    public int getPlayersRevived() { return playersRevived; }
    
    // State checks
    public boolean isActive() { return state == InstanceState.ACTIVE; }
    public boolean isCompleted() { return state == InstanceState.COMPLETED; }
    public boolean isFailed() { return state == InstanceState.FAILED; }
    public boolean isFinished() { return state == InstanceState.COMPLETED || state == InstanceState.FAILED; }
    
    @Override
    public String toString() {
        return String.format("DungeonInstance{id='%s', template='%s', state=%s, players=%d, duration=%s}", 
            instanceId, templateId, state, players.size(), getFormattedDuration());
    }
}
