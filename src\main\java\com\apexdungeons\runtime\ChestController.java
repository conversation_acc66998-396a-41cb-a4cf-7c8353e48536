package com.apexdungeons.runtime;

import com.apexdungeons.DungeonX;
import com.apexdungeons.core.ChestDef;
import com.apexdungeons.core.DungeonInstance;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.Chest;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Controls chest behavior and loot regeneration in dungeon instances.
 * Handles per-instance cooldowns and automatic refilling.
 */
public class ChestController implements Listener {
    
    private final DungeonX plugin;
    private final DungeonInstance instance;
    private final Map<String, Location> chestLocations = new ConcurrentHashMap<>();
    private final Map<String, Long> lastRefillTimes = new ConcurrentHashMap<>();
    
    private boolean active = false;
    private BukkitRunnable refillTask;
    
    public ChestController(DungeonX plugin, DungeonInstance instance) {
        this.plugin = plugin;
        this.instance = instance;
        
        // Map chest locations
        for (ChestDef chest : instance.getActiveChests()) {
            Location chestLoc = chest.getLocation();
            // Translate to instance world
            Location instanceLoc = new Location(instance.getWorld(), 
                chestLoc.getX(), chestLoc.getY(), chestLoc.getZ());
            chestLocations.put(chest.getId(), instanceLoc);
        }
    }
    
    /**
     * Start the chest controller.
     */
    public void start() {
        if (active) return;
        
        active = true;
        
        // Register event listener
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        
        // Start refill check task (runs every 30 seconds)
        refillTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!active || !instance.isActive()) {
                    cancel();
                    return;
                }
                checkRefills();
            }
        };
        refillTask.runTaskTimer(plugin, 600L, 600L); // Every 30 seconds
        
        // Initialize all chests with loot
        initializeChests();
        
        plugin.getLogger().info("Started chest controller for instance: " + instance.getInstanceId());
    }
    
    /**
     * Stop the chest controller.
     */
    public void stop() {
        active = false;
        
        // Cancel refill task
        if (refillTask != null) {
            refillTask.cancel();
            refillTask = null;
        }
        
        plugin.getLogger().info("Stopped chest controller for instance: " + instance.getInstanceId());
    }
    
    /**
     * Initialize all chests with initial loot.
     */
    private void initializeChests() {
        for (ChestDef chestDef : instance.getActiveChests()) {
            Location chestLoc = chestLocations.get(chestDef.getId());
            if (chestLoc != null) {
                createChestIfNeeded(chestLoc, chestDef);
                fillChest(chestLoc, chestDef);
            }
        }
    }
    
    /**
     * Create a chest block if it doesn't exist.
     */
    private void createChestIfNeeded(Location location, ChestDef chestDef) {
        Block block = location.getBlock();
        if (block.getType() != Material.CHEST) {
            block.setType(Material.CHEST);
            
            // Set custom name if specified
            if (block.getState() instanceof Chest) {
                Chest chest = (Chest) block.getState();
                chest.setCustomName(chestDef.getDisplayName());
                chest.update();
            }
        }
    }
    
    /**
     * Fill a chest with loot.
     */
    private void fillChest(Location location, ChestDef chestDef) {
        Block block = location.getBlock();
        if (!(block.getState() instanceof Chest)) {
            return;
        }
        
        Chest chest = (Chest) block.getState();
        Inventory inventory = chest.getInventory();
        
        // Clear existing items
        inventory.clear();
        
        // Generate loot based on loot table
        List<ItemStack> loot = generateLoot(chestDef.getLootTableId());
        
        // Add loot to chest with some randomization
        Random random = new Random();
        for (ItemStack item : loot) {
            if (item != null && item.getType() != Material.AIR) {
                int slot = random.nextInt(inventory.getSize());
                // Find empty slot if the random one is occupied
                while (inventory.getItem(slot) != null) {
                    slot = (slot + 1) % inventory.getSize();
                }
                inventory.setItem(slot, item);
            }
        }
        
        // Record refill time
        lastRefillTimes.put(chestDef.getId(), System.currentTimeMillis());
        chestDef.recordRefill(instance.getInstanceId());
        
        // Show refill effects
        if (chestDef.isShowParticles()) {
            showRefillEffects(location);
        }
        
        plugin.getLogger().fine("Filled chest " + chestDef.getId() + " with " + loot.size() + " items");
    }
    
    /**
     * Generate loot based on loot table ID.
     */
    private List<ItemStack> generateLoot(String lootTableId) {
        List<ItemStack> loot = new ArrayList<>();
        
        // Get loot configuration
        var lootConfig = plugin.getConfigManager().getLootConfig();
        var lootTable = lootConfig.getConfigurationSection("tables." + lootTableId);
        
        if (lootTable == null) {
            // Fallback to default loot
            loot.add(new ItemStack(Material.GOLD_INGOT, 1 + new Random().nextInt(3)));
            loot.add(new ItemStack(Material.DIAMOND, 1));
            return loot;
        }
        
        // Process loot table entries
        for (String key : lootTable.getKeys(false)) {
            var entry = lootTable.getConfigurationSection(key);
            if (entry == null) continue;
            
            String materialName = entry.getString("material", "GOLD_INGOT");
            int minAmount = entry.getInt("min-amount", 1);
            int maxAmount = entry.getInt("max-amount", 1);
            double chance = entry.getDouble("chance", 1.0);
            
            // Roll for chance
            if (new Random().nextDouble() <= chance) {
                try {
                    Material material = Material.valueOf(materialName.toUpperCase());
                    int amount = minAmount + new Random().nextInt(Math.max(1, maxAmount - minAmount + 1));
                    loot.add(new ItemStack(material, amount));
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material in loot table " + lootTableId + ": " + materialName);
                }
            }
        }
        
        return loot;
    }
    
    /**
     * Check all chests for refill conditions.
     */
    private void checkRefills() {
        for (ChestDef chestDef : instance.getActiveChests()) {
            if (chestDef.canRefill(instance.getInstanceId())) {
                Location chestLoc = chestLocations.get(chestDef.getId());
                if (chestLoc != null && isChestEmpty(chestLoc)) {
                    fillChest(chestLoc, chestDef);
                }
            }
        }
    }
    
    /**
     * Check if a chest is empty or nearly empty.
     */
    private boolean isChestEmpty(Location location) {
        Block block = location.getBlock();
        if (!(block.getState() instanceof Chest)) {
            return false;
        }
        
        Chest chest = (Chest) block.getState();
        Inventory inventory = chest.getInventory();
        
        int itemCount = 0;
        for (ItemStack item : inventory.getContents()) {
            if (item != null && item.getType() != Material.AIR) {
                itemCount++;
            }
        }
        
        // Consider empty if less than 25% full
        return itemCount < (inventory.getSize() * 0.25);
    }
    
    /**
     * Show refill effects.
     */
    private void showRefillEffects(Location location) {
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.ENCHANT, 
                location.clone().add(0.5, 1, 0.5), 10, 0.5, 0.5, 0.5, 0.1);
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, 
                location.clone().add(0.5, 1, 0.5), 5, 0.3, 0.3, 0.3, 0.1);
        }
        
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.BLOCK_CHEST_OPEN, 0.5f, 1.2f);
        }
    }
    
    /**
     * Handle chest opening events.
     */
    @EventHandler
    public void onChestOpen(InventoryOpenEvent event) {
        if (!active || !(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        
        // Check if player is in this instance
        if (!instance.hasPlayer(player)) {
            return;
        }
        
        // Check if opened inventory is a chest in this instance
        if (!(event.getInventory().getHolder() instanceof Chest)) {
            return;
        }
        
        Chest chest = (Chest) event.getInventory().getHolder();
        Location chestLoc = chest.getLocation();
        
        // Find matching chest definition
        ChestDef chestDef = findChestDef(chestLoc);
        if (chestDef != null) {
            // Record chest opening
            chestDef.recordOpen(instance.getInstanceId());
            instance.recordChestOpen();
            
            plugin.getLogger().fine("Player " + player.getName() + " opened chest " + chestDef.getId());
        }
    }
    
    /**
     * Find chest definition by location.
     */
    private ChestDef findChestDef(Location location) {
        for (Map.Entry<String, Location> entry : chestLocations.entrySet()) {
            if (entry.getValue().distance(location) < 1.0) {
                return instance.getChest(entry.getKey());
            }
        }
        return null;
    }
    
    /**
     * Force refill a specific chest.
     */
    public void forceRefill(String chestId) {
        ChestDef chestDef = instance.getChest(chestId);
        Location chestLoc = chestLocations.get(chestId);
        
        if (chestDef != null && chestLoc != null) {
            fillChest(chestLoc, chestDef);
        }
    }
    
    /**
     * Get chest statistics.
     */
    public Map<String, Object> getChestStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("active", active);
        stats.put("totalChests", instance.getActiveChests().size());
        
        int refillableChests = 0;
        for (ChestDef chest : instance.getActiveChests()) {
            if (chest.canRefill(instance.getInstanceId())) {
                refillableChests++;
            }
        }
        stats.put("refillableChests", refillableChests);
        
        return stats;
    }
    
    /**
     * Check if the controller is active.
     */
    public boolean isActive() {
        return active;
    }
}
