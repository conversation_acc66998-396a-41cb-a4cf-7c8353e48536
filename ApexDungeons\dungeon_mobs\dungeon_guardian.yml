# Dungeon Guardian Configuration
# A powerful boss-level iron golem guardian

display_name: "Dungeon Guardian"
description: "A massive iron construct that guards important areas"
category: "boss"
difficulty: "boss"
icon: "IRON_BLOCK"

# Mob spawning configuration
mob_type: "IRON_GOLEM"
spawn_radius: 10
cooldown:
  min: 300
  max: 600
max_concurrent: 1
is_boss: true

# Commands to execute when spawning
spawn_commands:
  - "summon iron_golem %x% %y% %z% {CustomName:'\"Dungeon Guardian\"', Health:200f, Attributes:[{Name:generic.max_health,Base:200},{Name:generic.attack_damage,Base:15},{Name:generic.knockback_resistance,Base:1.0}], ActiveEffects:[{Id:5,Amplifier:1,Duration:999999,ShowParticles:0b}]}"
  - "particle explosion %x% %y% %z% 2 2 2 0.1 5"
  - "playsound entity.wither.spawn master @a %x% %y% %z% 1.0 0.8"

# Information shown to builders
builder_info:
  - "BOSS-LEVEL ENEMY"
  - "200 HP with Strength II"
  - "Knockback immunity"
  - "Massive damage output"
  - "Long respawn timer (5-10 min)"
  - "Use for final rooms only"
