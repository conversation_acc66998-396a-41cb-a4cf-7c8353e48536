# Fire Spider Configuration
# A magical spider that sets enemies on fire

display_name: "Fire Spider"
description: "A magical arachnid that inflicts burning damage"
category: "magical"
difficulty: "normal"
icon: "SPIDER_EYE"

# Mob spawning configuration
mob_type: "SPIDER"
spawn_radius: 7
cooldown:
  min: 20
  max: 40
max_concurrent: 3
is_boss: false

# Commands to execute when spawning
spawn_commands:
  - "summon spider %x% %y% %z% {CustomName:'\"Fire Spider\"', ActiveEffects:[{Id:12,Amplifier:0,Duration:999999,ShowParticles:0b}], Attributes:[{Name:generic.attack_damage,Base:6}]}"
  - "particle flame %x% %y% %z% 0.5 0.5 0.5 0.1 10"

# Information shown to builders
builder_info:
  - "Fast magical attacker"
  - "Sets targets on fire"
  - "Enhanced damage output"
  - "Spawns in groups of up to 3"
  - "Quick spawn rate (20-40s)"
