name: ApexDungeons
main: com.apexdungeons.DungeonXMinimal
version: ${version}
api-version: "1.21"
folia-supported: true
softdepend:
  - MythicMobs
  - FastAsyncWorldEdit
  - PlaceholderAPI
  - Vault
authors: ["Vexy"]
description: |
  DungeonX is a commercial-quality dungeon system for Paper 1.21+. Features true instancing,
  GUI-driven workflow, reliable saving, MythicMobs integration, and professional-grade tools.
website: https://github.com/VexyMC/DungeonX
commands:
  dgn:
    description: Main command for DungeonX - GUI and hotkey driven dungeon system.
    usage: /dgn [help]
    aliases: [dungeon, dungeonx, dx]
permissions:
  dungeonx.use:
    description: Allows access to the main dungeon GUI and basic dungeon interaction.
    default: true
  dungeonx.admin:
    description: Allows administrative control over dungeon templates and system management.
    default: op
  dungeonx.build:
    description: Allows access to Build Mode for creating and editing dungeon templates.
    default: op
  dungeonx.instance:
    description: Allows creating and managing dungeon instances.
    default: true
