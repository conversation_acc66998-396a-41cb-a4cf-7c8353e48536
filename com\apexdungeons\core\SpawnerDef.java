package com.apexdungeons.core;

import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import java.util.List;
import java.util.ArrayList;

/**
 * Represents a spawner definition with mob types and spawn configuration
 */
public class SpawnerDef {
    private String id;
    private Location location;
    private List<String> mobTypes;
    private List<Integer> mobWeights;
    private int maxMobs;
    private int spawnDelay;
    private int spawnRange;
    private boolean enabled;
    private String spawnerType; // "vanilla" or "mythic"
    
    public SpawnerDef(String id) {
        this.id = id;
        this.mobTypes = new ArrayList<>();
        this.mobWeights = new ArrayList<>();
        this.maxMobs = 4;
        this.spawnDelay = 20;
        this.spawnRange = 4;
        this.enabled = true;
        this.spawnerType = "vanilla";
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }
    
    public List<String> getMobTypes() { return mobTypes; }
    public void setMobTypes(List<String> mobTypes) { this.mobTypes = mobTypes; }
    
    public List<Integer> getMobWeights() { return mobWeights; }
    public void setMobWeights(List<Integer> mobWeights) { this.mobWeights = mobWeights; }
    
    public int getMaxMobs() { return maxMobs; }
    public void setMaxMobs(int maxMobs) { this.maxMobs = maxMobs; }
    
    public int getSpawnDelay() { return spawnDelay; }
    public void setSpawnDelay(int spawnDelay) { this.spawnDelay = spawnDelay; }
    
    public int getSpawnRange() { return spawnRange; }
    public void setSpawnRange(int spawnRange) { this.spawnRange = spawnRange; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public String getSpawnerType() { return spawnerType; }
    public void setSpawnerType(String spawnerType) { this.spawnerType = spawnerType; }
    
    public void addMobType(String mobType, int weight) {
        this.mobTypes.add(mobType);
        this.mobWeights.add(weight);
    }
    
    public void removeMobType(String mobType) {
        int index = this.mobTypes.indexOf(mobType);
        if (index >= 0) {
            this.mobTypes.remove(index);
            this.mobWeights.remove(index);
        }
    }
    
    public String getRandomMobType() {
        if (mobTypes.isEmpty()) return "ZOMBIE";
        
        int totalWeight = mobWeights.stream().mapToInt(Integer::intValue).sum();
        if (totalWeight <= 0) return mobTypes.get(0);
        
        int random = (int) (Math.random() * totalWeight);
        int currentWeight = 0;
        
        for (int i = 0; i < mobTypes.size(); i++) {
            currentWeight += mobWeights.get(i);
            if (random < currentWeight) {
                return mobTypes.get(i);
            }
        }
        
        return mobTypes.get(0);
    }
}
