package com.apexdungeons.integration;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import java.util.List;

/**
 * Interface for mob adapters to handle different mob systems
 */
public interface MobAdapter {
    
    /**
     * Spawn a mob at the specified location
     * @param mobType The type of mob to spawn
     * @param location The location to spawn the mob
     * @return The spawned entity, or null if failed
     */
    Entity spawnMob(String mobType, Location location);
    
    /**
     * Check if a mob type is valid for this adapter
     * @param mobType The mob type to check
     * @return true if valid, false otherwise
     */
    boolean isValidMobType(String mobType);
    
    /**
     * Get all available mob types for this adapter
     * @return List of available mob types
     */
    List<String> getAvailableMobTypes();
    
    /**
     * Get the display name for a mob type
     * @param mobType The mob type
     * @return The display name
     */
    String getMobDisplayName(String mobType);
    
    /**
     * Check if this adapter is enabled and available
     * @return true if enabled, false otherwise
     */
    boolean isEnabled();
    
    /**
     * Get the name of this adapter
     * @return The adapter name
     */
    String getAdapterName();
    
    /**
     * Initialize the adapter
     */
    void initialize();
    
    /**
     * Shutdown the adapter
     */
    void shutdown();
}
