package com.apexdungeons.core;

import com.apexdungeons.DungeonXMinimal;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Template management system.
 */
public class TemplateManager {
    
    private final DungeonXMinimal plugin;
    private final DataStore dataStore;
    private final Map<String, DungeonTemplate> templates = new ConcurrentHashMap<>();
    
    public TemplateManager(DungeonXMinimal plugin, DataStore dataStore) {
        this.plugin = plugin;
        this.dataStore = dataStore;
    }
    
    public void loadTemplates() {
        plugin.getLogger().info("Loading templates...");
    }
    
    public int getTemplateCount() {
        return templates.size();
    }
    
    public List<DungeonTemplate> getPublishedTemplates() {
        return new ArrayList<>(templates.values());
    }
    
    public static class DungeonTemplate {
        private String name;
        private boolean published;
        
        public DungeonTemplate(String name) {
            this.name = name;
            this.published = false;
        }
        
        public String getName() { return name; }
        public boolean isPublished() { return published; }
    }
}
