package com.apexdungeons.core;

import org.bukkit.Location;
import java.util.List;
import java.util.ArrayList;

/**
 * Represents a dungeon template with configuration for spawners, bosses, and chests
 */
public class DungeonTemplate {
    private String name;
    private String description;
    private int minLevel;
    private int maxLevel;
    private List<String> schematicFiles;
    private List<SpawnerDef> spawners;
    private List<BossDef> bosses;
    private List<ChestDef> chests;
    private boolean enabled;
    private boolean published;
    private String id;
    private String creator;
    private String schematicFile;
    private Location spawnLocation;
    private Location exitLocation;
    
    public DungeonTemplate(String name) {
        this.name = name;
        this.id = name.toLowerCase().replace(" ", "_");
        this.description = "";
        this.minLevel = 1;
        this.maxLevel = 100;
        this.schematicFiles = new ArrayList<>();
        this.spawners = new ArrayList<>();
        this.bosses = new ArrayList<>();
        this.chests = new ArrayList<>();
        this.enabled = true;
        this.published = false;
        this.creator = "Unknown";
        this.schematicFile = null;
    }
    
    // Getters and setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public int getMinLevel() { return minLevel; }
    public void setMinLevel(int minLevel) { this.minLevel = minLevel; }
    
    public int getMaxLevel() { return maxLevel; }
    public void setMaxLevel(int maxLevel) { this.maxLevel = maxLevel; }
    
    public List<String> getSchematicFiles() { return schematicFiles; }
    public void setSchematicFiles(List<String> schematicFiles) { this.schematicFiles = schematicFiles; }
    
    public List<SpawnerDef> getSpawners() { return spawners; }
    public void setSpawners(List<SpawnerDef> spawners) { this.spawners = spawners; }
    
    public List<BossDef> getBosses() { return bosses; }
    public void setBosses(List<BossDef> bosses) { this.bosses = bosses; }
    
    public List<ChestDef> getChests() { return chests; }
    public void setChests(List<ChestDef> chests) { this.chests = chests; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public void addSpawner(SpawnerDef spawner) {
        this.spawners.add(spawner);
    }
    
    public void addBoss(BossDef boss) {
        this.bosses.add(boss);
    }
    
    public void addChest(ChestDef chest) {
        this.chests.add(chest);
    }
    
    public void removeSpawner(SpawnerDef spawner) {
        this.spawners.remove(spawner);
    }
    
    public void removeBoss(BossDef boss) {
        this.bosses.remove(boss);
    }
    
    public void removeChest(ChestDef chest) {
        this.chests.remove(chest);
    }
    
    public boolean isPublished() { return published; }
    public void setPublished(boolean published) { this.published = published; }
    
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getCreator() { return creator; }
    public void setCreator(String creator) { this.creator = creator; }
    
    public String getSchematicFile() { return schematicFile; }
    public void setSchematicFile(String schematicFile) { this.schematicFile = schematicFile; }
    
    public Location getSpawnLocation() { return spawnLocation; }
    public void setSpawnLocation(Location spawnLocation) { this.spawnLocation = spawnLocation; }
    
    public Location getExitLocation() { return exitLocation; }
    public void setExitLocation(Location exitLocation) { this.exitLocation = exitLocation; }
    
    public DungeonTemplate copy() {
        DungeonTemplate copy = new DungeonTemplate(this.name + "_copy");
        copy.description = this.description;
        copy.minLevel = this.minLevel;
        copy.maxLevel = this.maxLevel;
        copy.schematicFiles = new ArrayList<>(this.schematicFiles);
        copy.spawners = new ArrayList<>(this.spawners);
        copy.bosses = new ArrayList<>(this.bosses);
        copy.chests = new ArrayList<>(this.chests);
        copy.enabled = this.enabled;
        copy.published = false; // Copies are not published by default
        copy.creator = this.creator;
        copy.schematicFile = this.schematicFile;
        copy.spawnLocation = this.spawnLocation;
        copy.exitLocation = this.exitLocation;
        return copy;
    }
}
