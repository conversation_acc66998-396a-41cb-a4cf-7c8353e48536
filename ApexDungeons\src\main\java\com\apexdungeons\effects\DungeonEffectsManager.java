package com.apexdungeons.effects;

import com.apexdungeons.DungeonX;
import com.apexdungeons.core.DungeonInstance;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.title.Title;
import net.kyori.adventure.bossbar.BossBar;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages visual effects, titles, sounds, particles, and boss bars for dungeon instances.
 * Provides comprehensive player feedback and atmospheric effects.
 */
public class DungeonEffectsManager {
    
    private final DungeonX plugin;
    private final Map<UUID, BossBar> playerBossBars = new ConcurrentHashMap<>();
    private final Map<String, BukkitRunnable> activeEffects = new ConcurrentHashMap<>();
    
    public DungeonEffectsManager(DungeonX plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Show dungeon start effects to players.
     */
    public void showDungeonStartEffects(DungeonInstance instance) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        for (Player player : instance.getPlayers()) {
            // Title
            if (plugin.getConfigManager().areTitlesEnabled()) {
                Component title = Component.text("DUNGEON STARTED")
                    .color(NamedTextColor.GOLD)
                    .decorate(TextDecoration.BOLD);
                Component subtitle = Component.text(instance.getTemplate().getName())
                    .color(NamedTextColor.YELLOW);
                
                player.showTitle(Title.title(title, subtitle, 
                    Title.Times.times(Duration.ofMillis(500), Duration.ofSeconds(3), Duration.ofMillis(500))));
            }
            
            // Sound
            if (plugin.getConfigManager().areSoundsEnabled()) {
                player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.8f);
            }
            
            // Particles
            if (plugin.getConfigManager().areParticlesEnabled()) {
                Location loc = player.getLocation();
                player.getWorld().spawnParticle(Particle.DRAGON_BREATH, loc, 50, 2, 1, 2, 0.1);
                player.getWorld().spawnParticle(Particle.ENCHANT, loc, 30, 1, 1, 1, 0.5);
            }
        }
    }
    
    /**
     * Show dungeon completion effects.
     */
    public void showDungeonCompletionEffects(DungeonInstance instance, boolean success) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        for (Player player : instance.getPlayers()) {
            if (success) {
                showSuccessEffects(player, instance);
            } else {
                showFailureEffects(player, instance);
            }
        }
    }
    
    /**
     * Show success effects.
     */
    private void showSuccessEffects(Player player, DungeonInstance instance) {
        // Title
        if (plugin.getConfigManager().areTitlesEnabled()) {
            Component title = Component.text("VICTORY!")
                .color(NamedTextColor.GREEN)
                .decorate(TextDecoration.BOLD);
            Component subtitle = Component.text("Dungeon Completed in " + instance.getFormattedDuration())
                .color(NamedTextColor.GOLD);
            
            player.showTitle(Title.title(title, subtitle, 
                Title.Times.times(Duration.ofMillis(500), Duration.ofSeconds(4), Duration.ofSeconds(1))));
        }
        
        // Sound
        if (plugin.getConfigManager().areSoundsEnabled()) {
            player.playSound(player.getLocation(), Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
        }
        
        // Particles
        if (plugin.getConfigManager().areParticlesEnabled()) {
            createVictoryFireworks(player.getLocation());
        }
    }
    
    /**
     * Show failure effects.
     */
    private void showFailureEffects(Player player, DungeonInstance instance) {
        // Title
        if (plugin.getConfigManager().areTitlesEnabled()) {
            Component title = Component.text("DEFEAT")
                .color(NamedTextColor.RED)
                .decorate(TextDecoration.BOLD);
            Component subtitle = Component.text("Better luck next time...")
                .color(NamedTextColor.GRAY);
            
            player.showTitle(Title.title(title, subtitle, 
                Title.Times.times(Duration.ofMillis(500), Duration.ofSeconds(3), Duration.ofSeconds(1))));
        }
        
        // Sound
        if (plugin.getConfigManager().areSoundsEnabled()) {
            player.playSound(player.getLocation(), Sound.ENTITY_WITHER_DEATH, 0.5f, 0.8f);
        }
        
        // Particles
        if (plugin.getConfigManager().areParticlesEnabled()) {
            Location loc = player.getLocation();
            player.getWorld().spawnParticle(Particle.SMOKE, loc, 20, 1, 1, 1, 0.1);
        }
    }
    
    /**
     * Show boss spawn effects.
     */
    public void showBossSpawnEffects(Location location, String bossName) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        // Particles
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.EXPLOSION, location, 5);
            location.getWorld().spawnParticle(Particle.LAVA, location, 20, 2, 1, 2, 0.1);
            location.getWorld().spawnParticle(Particle.FLAME, location, 30, 1, 2, 1, 0.2);
        }
        
        // Sound
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.ENTITY_WITHER_SPAWN, 2.0f, 0.7f);
        }
        
        // Notify nearby players
        for (Player player : location.getWorld().getPlayers()) {
            if (player.getLocation().distance(location) <= 50) {
                if (plugin.getConfigManager().areTitlesEnabled()) {
                    Component title = Component.text("BOSS AWAKENED")
                        .color(NamedTextColor.DARK_RED)
                        .decorate(TextDecoration.BOLD);
                    Component subtitle = Component.text(bossName)
                        .color(NamedTextColor.RED);
                    
                    player.showTitle(Title.title(title, subtitle, 
                        Title.Times.times(Duration.ofMillis(500), Duration.ofSeconds(2), Duration.ofMillis(500))));
                }
            }
        }
    }
    
    /**
     * Create and manage boss bar for a player.
     */
    public void createBossBar(Player player, String bossName, float health) {
        if (!plugin.getConfigManager().areBossBarsEnabled()) return;
        
        BossBar bossBar = BossBar.bossBar(
            Component.text(bossName).color(NamedTextColor.RED),
            health,
            BossBar.Color.RED,
            BossBar.Overlay.PROGRESS
        );
        
        player.showBossBar(bossBar);
        playerBossBars.put(player.getUniqueId(), bossBar);
    }
    
    /**
     * Update boss bar health.
     */
    public void updateBossBar(Player player, float health) {
        BossBar bossBar = playerBossBars.get(player.getUniqueId());
        if (bossBar != null) {
            bossBar.progress(Math.max(0, Math.min(1, health)));
            
            // Change color based on health
            if (health < 0.25f) {
                bossBar.color(BossBar.Color.RED);
            } else if (health < 0.5f) {
                bossBar.color(BossBar.Color.YELLOW);
            } else {
                bossBar.color(BossBar.Color.GREEN);
            }
        }
    }
    
    /**
     * Remove boss bar from player.
     */
    public void removeBossBar(Player player) {
        BossBar bossBar = playerBossBars.remove(player.getUniqueId());
        if (bossBar != null) {
            player.hideBossBar(bossBar);
        }
    }
    
    /**
     * Show boss phase change effects.
     */
    public void showBossPhaseEffects(Location location, String phaseName, int phaseNumber) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        // Particles
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.SONIC_BOOM, location, 1);
            location.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, location, 50, 3, 3, 3, 0.3);
        }
        
        // Sound
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.2f);
        }
        
        // Notify nearby players
        for (Player player : location.getWorld().getPlayers()) {
            if (player.getLocation().distance(location) <= 50) {
                if (plugin.getConfigManager().areTitlesEnabled()) {
                    Component title = Component.text("PHASE " + phaseNumber)
                        .color(NamedTextColor.DARK_PURPLE)
                        .decorate(TextDecoration.BOLD);
                    Component subtitle = Component.text(phaseName)
                        .color(NamedTextColor.LIGHT_PURPLE);
                    
                    player.showTitle(Title.title(title, subtitle, 
                        Title.Times.times(Duration.ofMillis(300), Duration.ofSeconds(2), Duration.ofMillis(300))));
                }
            }
        }
    }
    
    /**
     * Show wave spawn effects.
     */
    public void showWaveSpawnEffects(Location location, int waveNumber, int mobCount) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        // Particles
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.PORTAL, location, 30, 2, 1, 2, 0.5);
            location.getWorld().spawnParticle(Particle.WITCH, location, 10, 1, 1, 1, 0.1);
        }
        
        // Sound
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.ENTITY_ZOMBIE_VILLAGER_CONVERTED, 1.0f, 0.8f);
        }
        
        // Notify nearby players
        for (Player player : location.getWorld().getPlayers()) {
            if (player.getLocation().distance(location) <= 30) {
                player.sendMessage(Component.text("Wave " + waveNumber + " - " + mobCount + " enemies incoming!")
                    .color(NamedTextColor.YELLOW));
            }
        }
    }
    
    /**
     * Create victory fireworks effect.
     */
    private void createVictoryFireworks(Location location) {
        String effectId = "fireworks_" + System.currentTimeMillis();
        
        BukkitRunnable fireworksTask = new BukkitRunnable() {
            int count = 0;
            
            @Override
            public void run() {
                if (count >= 10) {
                    cancel();
                    activeEffects.remove(effectId);
                    return;
                }
                
                // Random firework location around player
                Location fireworkLoc = location.clone().add(
                    (Math.random() - 0.5) * 10,
                    Math.random() * 5 + 5,
                    (Math.random() - 0.5) * 10
                );
                
                // Colorful particles
                Particle[] colors = {Particle.DUST, Particle.ENCHANT, Particle.HAPPY_VILLAGER};
                Particle particle = colors[(int) (Math.random() * colors.length)];
                
                location.getWorld().spawnParticle(particle, fireworkLoc, 20, 0.5, 0.5, 0.5, 0.1);
                location.getWorld().playSound(fireworkLoc, Sound.ENTITY_FIREWORK_ROCKET_BLAST, 0.5f, 1.0f + (float) Math.random());
                
                count++;
            }
        };
        
        fireworksTask.runTaskTimer(plugin, 0L, 10L);
        activeEffects.put(effectId, fireworksTask);
    }
    
    /**
     * Show chest refill effects.
     */
    public void showChestRefillEffects(Location location) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.ENCHANT, 
                location.clone().add(0.5, 1, 0.5), 15, 0.5, 0.5, 0.5, 0.2);
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, 
                location.clone().add(0.5, 1, 0.5), 5, 0.3, 0.3, 0.3, 0.1);
        }
        
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.BLOCK_CHEST_OPEN, 0.7f, 1.2f);
        }
    }
    
    /**
     * Show ambient dungeon effects.
     */
    public void startAmbientEffects(DungeonInstance instance) {
        if (!plugin.getConfigManager().areEffectsEnabled()) return;
        
        String effectId = "ambient_" + instance.getInstanceId();
        
        BukkitRunnable ambientTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!instance.isActive()) {
                    cancel();
                    activeEffects.remove(effectId);
                    return;
                }
                
                // Random ambient effects for atmosphere
                for (Player player : instance.getPlayers()) {
                    if (Math.random() < 0.1) { // 10% chance per tick
                        Location loc = player.getLocation().add(
                            (Math.random() - 0.5) * 20,
                            Math.random() * 10,
                            (Math.random() - 0.5) * 20
                        );
                        
                        if (plugin.getConfigManager().areParticlesEnabled()) {
                            player.getWorld().spawnParticle(Particle.SMOKE, loc, 1, 0.1, 0.1, 0.1, 0.01);
                        }
                        
                        if (plugin.getConfigManager().areSoundsEnabled() && Math.random() < 0.05) {
                            Sound[] ambientSounds = {
                                Sound.AMBIENT_CAVE, Sound.AMBIENT_CRIMSON_FOREST_MOOD,
                                Sound.AMBIENT_SOUL_SAND_VALLEY_MOOD
                            };
                            Sound sound = ambientSounds[(int) (Math.random() * ambientSounds.length)];
                            player.playSound(player.getLocation(), sound, 0.3f, 0.8f + (float) Math.random() * 0.4f);
                        }
                    }
                }
            }
        };
        
        ambientTask.runTaskTimer(plugin, 100L, 100L); // Every 5 seconds
        activeEffects.put(effectId, ambientTask);
    }
    
    /**
     * Stop all effects for an instance.
     */
    public void stopInstanceEffects(DungeonInstance instance) {
        // Remove boss bars for all players
        for (Player player : instance.getPlayers()) {
            removeBossBar(player);
        }
        
        // Cancel ambient effects
        String ambientEffectId = "ambient_" + instance.getInstanceId();
        BukkitRunnable ambientTask = activeEffects.remove(ambientEffectId);
        if (ambientTask != null) {
            ambientTask.cancel();
        }
    }
    
    /**
     * Cleanup all effects.
     */
    public void cleanup() {
        // Cancel all active effects
        for (BukkitRunnable task : activeEffects.values()) {
            task.cancel();
        }
        activeEffects.clear();
        
        // Remove all boss bars
        for (Map.Entry<UUID, BossBar> entry : playerBossBars.entrySet()) {
            Player player = plugin.getServer().getPlayer(entry.getKey());
            if (player != null) {
                player.hideBossBar(entry.getValue());
            }
        }
        playerBossBars.clear();
    }
}
