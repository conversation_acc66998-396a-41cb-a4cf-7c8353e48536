package com.apexdungeons;

import com.apexdungeons.commands.DungeonCommand;
import com.apexdungeons.core.*;
import com.apexdungeons.gui.ToolsGUI;
import com.apexdungeons.gui.LootConfigGUI;
import com.apexdungeons.gui.MobConfigGUI;
import com.apexdungeons.gui.BossConfigGUI;
import com.apexdungeons.integration.MobAdapter;
import com.apexdungeons.integration.MythicMobsAdapter;
import com.apexdungeons.integration.VanillaAdapter;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

/**
 * DungeonX - Commercial-quality dungeon system for Minecraft Paper 1.21+
 * 
 * Features:
 * - True instance isolation with dedicated worlds
 * - Atomic data persistence with corruption recovery
 * - Professional GUI-driven workflow
 * - Enhanced MythicMobs integration with fallback
 * - Comprehensive template and instance management
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DungeonXMinimal extends JavaPlugin {
    
    // Core Systems
    private ConfigManager configManager;
    private DataStore dataStore;
    private TemplateManager templateManager;
    private InstanceManager instanceManager;
    
    // Integration
    private MobAdapter mobAdapter;
    
    // GUI System
    private ToolsGUI toolsGUI;
    private LootConfigGUI lootConfigGUI;
    private MobConfigGUI mobConfigGUI;
    private BossConfigGUI bossConfigGUI;
    
    @Override
    public void onEnable() {
        getLogger().info("Starting DungeonX - Commercial Dungeon System");
        
        try {
            // Initialize core systems
            initializeCore();
            
            // Initialize integrations
            initializeIntegrations();
            
            // Initialize GUI system
            initializeGUI();
            
            // Register commands
            registerCommands();
            
            getLogger().info("DungeonX enabled successfully!");
            getLogger().info("Use /dgn to access the dungeon tools");
            
        } catch (Exception e) {
            getLogger().severe("Failed to enable DungeonX: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        getLogger().info("Shutting down DungeonX...");
        
        try {
            // Save all data
            if (dataStore != null) {
                dataStore.saveAll();
            }
            
            // Cleanup instances
            if (instanceManager != null) {
                instanceManager.shutdown();
            }
            
            getLogger().info("DungeonX disabled successfully!");
            
        } catch (Exception e) {
            getLogger().severe("Error during shutdown: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Initialize core systems.
     */
    private void initializeCore() {
        getLogger().info("Initializing core systems...");
        
        // Configuration
        configManager = new ConfigManager(this);
        configManager.loadConfigurations();
        
        // Data persistence
        dataStore = new DataStore(this);
        dataStore.initialize();
        
        // Template management
        templateManager = new TemplateManager(this, dataStore);
        templateManager.loadTemplates();
        
        // Instance management
        instanceManager = new InstanceManager(this);
        
        getLogger().info("Core systems initialized!");
    }
    
    /**
     * Initialize integrations.
     */
    private void initializeIntegrations() {
        getLogger().info("Initializing integrations...");
        
        // MythicMobs integration
        if (getServer().getPluginManager().getPlugin("MythicMobs") != null) {
            try {
                mobAdapter = new MythicMobsAdapter();
                getLogger().info("MythicMobs integration enabled!");
            } catch (Exception e) {
                getLogger().warning("Failed to initialize MythicMobs integration: " + e.getMessage());
                mobAdapter = new VanillaAdapter();
                getLogger().info("Using vanilla mob adapter as fallback");
            }
        } else {
            mobAdapter = new VanillaAdapter();
            getLogger().info("MythicMobs not found, using vanilla mob adapter");
        }
        
        getLogger().info("Integrations initialized!");
    }
    
    /**
     * Initialize GUI system.
     */
    private void initializeGUI() {
        getLogger().info("Initializing GUI system...");
        
        toolsGUI = new ToolsGUI(this);
        lootConfigGUI = new LootConfigGUI(this);
        mobConfigGUI = new MobConfigGUI(this);
        bossConfigGUI = new BossConfigGUI(this);
        
        getLogger().info("GUI system initialized!");
    }
    
    /**
     * Register commands.
     */
    private void registerCommands() {
        getLogger().info("Registering commands...");
        
        getCommand("dgn").setExecutor(new DungeonCommand(this));
        
        getLogger().info("Commands registered!");
    }
    
    // Getters for core systems
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public DataStore getDataStore() {
        return dataStore;
    }
    
    public TemplateManager getTemplateManager() {
        return templateManager;
    }
    
    public InstanceManager getInstanceManager() {
        return instanceManager;
    }
    
    public MobAdapter getMobAdapter() {
        return mobAdapter;
    }
    
    public ToolsGUI getToolsGUI() {
        return toolsGUI;
    }
    
    public LootConfigGUI getLootConfigGUI() {
        return lootConfigGUI;
    }
    
    public MobConfigGUI getMobConfigGUI() {
        return mobConfigGUI;
    }
    
    public BossConfigGUI getBossConfigGUI() {
        return bossConfigGUI;
    }
    
    public SchematicManager getSchematicManager() {
        // Return a stub schematic manager for now
        return new SchematicManager(this);
    }
    
    /**
     * Get plugin version.
     */
    public String getPluginVersion() {
        return getDescription().getVersion();
    }
    
    /**
     * Check if debug mode is enabled.
     */
    public boolean isDebugMode() {
        return configManager != null && configManager.isDebugMode();
    }
    
    /**
     * Log debug message.
     */
    public void debug(String message) {
        if (isDebugMode()) {
            getLogger().info("[DEBUG] " + message);
        }
    }
    
    /**
     * Log warning message.
     */
    public void warn(String message) {
        getLogger().warning(message);
    }
    
    /**
     * Log error message.
     */
    public void error(String message) {
        getLogger().severe(message);
    }
    
    /**
     * Log error with exception.
     */
    public void error(String message, Throwable throwable) {
        getLogger().log(Level.SEVERE, message, throwable);
    }
}
