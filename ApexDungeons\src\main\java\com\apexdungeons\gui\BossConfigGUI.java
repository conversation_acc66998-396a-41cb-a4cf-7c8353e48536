package com.apexdungeons.gui;

import com.apexdungeons.DungeonXMinimal;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Advanced GUI for managing boss configurations with full CRUD operations.
 * Supports creating, editing, and deleting boss definitions with custom stats.
 */
public class BossConfigGUI implements Listener {
    
    private final DungeonXMinimal plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    private final Map<UUID, String> editingBoss = new HashMap<>();
    private final Map<UUID, String> chatInputMode = new HashMap<>();
    private final Set<UUID> awaitingInput = new HashSet<>();
    
    public BossConfigGUI(DungeonXMinimal plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the main boss configuration GUI.
     */
    public void openBossConfig(Player player) {
        if (!player.hasPermission("dungeonx.admin")) {
            player.sendMessage("§cYou don't have permission to configure bosses.");
            return;
        }
        
        Inventory gui = createBossConfigInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create the main boss configuration inventory.
     */
    private Inventory createBossConfigInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lBoss Configuration");
        
        // Header
        gui.setItem(4, createItem(Material.WITHER_SKELETON_SKULL, "§e§lBoss Manager", 
            "§7Manage all boss definitions for dungeons", 
            "§7Create, edit, and delete boss configurations", "", 
            "§aTotal Bosses: §e" + getBosses().size()));
        
        // Load existing bosses
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection bosses = bossConfig.getConfigurationSection("bosses");
        
        int slot = 9;
        if (bosses != null) {
            for (String bossName : bosses.getKeys(false)) {
                if (slot >= 45) break; // Leave space for controls
                
                ConfigurationSection boss = bosses.getConfigurationSection(bossName);
                List<String> lore = new ArrayList<>();
                lore.add("§7Boss: §e" + bossName);
                
                if (boss != null) {
                    String type = boss.getString("type", "VANILLA:WITHER");
                    double healthMult = boss.getDouble("healthMultiplier", 1.0);
                    double damageMult = boss.getDouble("damageMultiplier", 1.0);
                    boolean announce = boss.getBoolean("announce", true);
                    
                    lore.add("§7Type: §a" + type);
                    lore.add("§7Health: §c" + (healthMult * 100) + "%");
                    lore.add("§7Damage: §6" + (damageMult * 100) + "%");
                    lore.add("§7Announce: " + (announce ? "§aYes" : "§cNo"));
                    
                    // Add type-specific info
                    if (type.startsWith("VANILLA:")) {
                        lore.add("§7Vanilla Minecraft boss");
                    } else if (type.startsWith("MM:")) {
                        lore.add("§7MythicMobs boss");
                        lore.add("§7Requires MythicMobs plugin");
                    }
                }
                
                lore.add("");
                lore.add("§eLeft-click: §7Edit boss");
                lore.add("§eRight-click: §7Delete boss");
                lore.add("§eShift-click: §7Duplicate boss");
                lore.add("§eMiddle-click: §7Test spawn boss");
                
                Material icon = getBossIcon(bossName, boss != null ? boss.getString("type", "VANILLA:WITHER") : "VANILLA:WITHER");
                gui.setItem(slot, createItem(icon, "§c§l" + bossName, lore.toArray(new String[0])));
                slot++;
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "§a§lCreate New Boss", 
            "§7Create a new boss definition", "", "§aClick to create"));
        
        gui.setItem(46, createItem(Material.BOOK, "§e§lImport Boss", 
            "§7Import boss from file", "", "§aClick to import"));
        
        gui.setItem(47, createItem(Material.WRITABLE_BOOK, "§6§lExport All", 
            "§7Export all bosses to file", "", "§aClick to export"));
        
        gui.setItem(48, createItem(Material.DRAGON_HEAD, "§d§lBoss Templates", 
            "§7Browse boss templates", "", "§aClick to browse"));
        
        gui.setItem(49, createItem(Material.REDSTONE, "§c§lReload Config", 
            "§7Reload boss configuration", "", "§aClick to reload"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Open boss editor for specific boss.
     */
    public void openBossEditor(Player player, String bossName) {
        editingBoss.put(player.getUniqueId(), bossName);
        
        Inventory gui = createBossEditorInventory(player, bossName);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create boss editor inventory.
     */
    private Inventory createBossEditorInventory(Player player, String bossName) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lEditing Boss: " + bossName);
        
        // Load boss data
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection boss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        String type = boss != null ? boss.getString("type", "VANILLA:WITHER") : "VANILLA:WITHER";
        double healthMult = boss != null ? boss.getDouble("healthMultiplier", 1.0) : 1.0;
        double damageMult = boss != null ? boss.getDouble("damageMultiplier", 1.0) : 1.0;
        boolean announce = boss != null ? boss.getBoolean("announce", true) : true;
        
        // Header
        gui.setItem(4, createItem(getBossIcon(bossName, type), "§e§lBoss: " + bossName, 
            "§7Editing boss configuration", "", "§7Type: §a" + type));
        
        // Boss Type Section
        gui.setItem(10, createItem(Material.NAME_TAG, "§a§lBoss Type", 
            "§7Current: §e" + type, "", 
            "§7Change the base mob type", "§7for this boss", "", "§aClick to change"));
        
        // Health Multiplier Section
        gui.setItem(11, createItem(Material.RED_DYE, "§c§lHealth Multiplier", 
            "§7Current: §e" + (healthMult * 100) + "%", "", 
            "§7Multiplies the base health", "§7of the boss mob", "", 
            "§eLeft-click: §7Increase by 0.1", "§eRight-click: §7Decrease by 0.1", 
            "§eShift-click: §7Set custom value"));
        
        // Damage Multiplier Section
        gui.setItem(12, createItem(Material.ORANGE_DYE, "§6§lDamage Multiplier", 
            "§7Current: §e" + (damageMult * 100) + "%", "", 
            "§7Multiplies the base damage", "§7of the boss mob", "", 
            "§eLeft-click: §7Increase by 0.1", "§eRight-click: §7Decrease by 0.1", 
            "§eShift-click: §7Set custom value"));
        
        // Announce Setting
        gui.setItem(13, createItem(announce ? Material.LIME_DYE : Material.GRAY_DYE, 
            "§b§lAnnounce Spawn", 
            "§7Current: " + (announce ? "§aEnabled" : "§cDisabled"), "", 
            "§7Whether to announce when", "§7this boss spawns", "", "§aClick to toggle"));
        
        // Advanced Settings Section
        gui.setItem(19, createItem(Material.COMPARATOR, "§d§lAdvanced Settings", 
            "§7Configure advanced boss", "§7properties and abilities", "", "§aClick to configure"));
        
        gui.setItem(20, createItem(Material.ENCHANTED_BOOK, "§5§lCustom Abilities", 
            "§7Add custom abilities and", "§7special attacks to boss", "", "§eFeature coming soon"));
        
        gui.setItem(21, createItem(Material.CHEST, "§e§lCustom Loot", 
            "§7Configure boss-specific", "§7loot drops", "", "§aClick to configure"));
        
        gui.setItem(22, createItem(Material.EXPERIENCE_BOTTLE, "§a§lRewards", 
            "§7Configure experience and", "§7other rewards", "", "§aClick to configure"));
        
        // Testing Section
        gui.setItem(28, createItem(Material.SPAWN_EGG, "§6§lTest Spawn", 
            "§7Spawn this boss for testing", "§7at your current location", "", 
            "§cWarning: This will spawn", "§ca real boss mob!", "", "§aClick to test"));
        
        gui.setItem(29, createItem(Material.PAPER, "§7§lBoss Statistics", 
            "§7View calculated boss stats", "§7and information", "", "§aClick to view"));
        
        // Control buttons
        gui.setItem(45, createItem(Material.PAPER, "§b§lRename Boss", 
            "§7Rename this boss", "", "§aClick to rename"));
        
        gui.setItem(46, createItem(Material.STRUCTURE_VOID, "§5§lClone Boss", 
            "§7Create a copy of this boss", "", "§aClick to clone"));
        
        gui.setItem(49, createItem(Material.LIME_DYE, "§a§lSave Changes", 
            "§7Save all modifications", "", "§aClick to save"));
        
        gui.setItem(52, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to main menu", "", "§aClick to go back"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Open boss templates browser.
     */
    public void openBossTemplates(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lBoss Templates");
        
        // Header
        gui.setItem(4, createItem(Material.DRAGON_HEAD, "§e§lBoss Templates", 
            "§7Pre-made boss configurations", "", "§7Click on a template to use it"));
        
        // Vanilla Boss Templates
        gui.setItem(9, createItem(Material.WITHER_SKELETON_SKULL, "§8§lThe Wither Lord", 
            "§7Type: VANILLA:WITHER", "§7Health: 200%", "§7Damage: 150%", "", 
            "§7A powerful wither boss", "§7with increased stats", "", "§aClick to create"));
        
        gui.setItem(10, createItem(Material.DRAGON_HEAD, "§5§lEnder Dragon", 
            "§7Type: VANILLA:ENDER_DRAGON", "§7Health: 100%", "§7Damage: 100%", "", 
            "§7The classic end boss", "", "§aClick to create"));
        
        gui.setItem(11, createItem(Material.IRON_BLOCK, "§7§lIron Golem Champion", 
            "§7Type: VANILLA:IRON_GOLEM", "§7Health: 300%", "§7Damage: 200%", "", 
            "§7A massive iron defender", "", "§aClick to create"));
        
        gui.setItem(12, createItem(Material.ZOMBIE_HEAD, "§2§lZombie King", 
            "§7Type: VANILLA:ZOMBIE", "§7Health: 500%", "§7Damage: 250%", "", 
            "§7An undead monarch", "", "§aClick to create"));
        
        gui.setItem(13, createItem(Material.SKELETON_SKULL, "§f§lBone Archer", 
            "§7Type: VANILLA:SKELETON", "§7Health: 200%", "§7Damage: 300%", "", 
            "§7A deadly marksman", "", "§aClick to create"));
        
        // MythicMobs Templates (if available)
        gui.setItem(18, createItem(Material.DIAMOND_SWORD, "§b§lMythicMobs Templates", 
            "§7Custom boss templates", "§7using MythicMobs", "", 
            plugin.getServer().getPluginManager().getPlugin("MythicMobs") != null ? 
            "§aClick to browse" : "§cRequires MythicMobs"));
        
        if (plugin.getServer().getPluginManager().getPlugin("MythicMobs") != null) {
            gui.setItem(19, createItem(Material.GOLDEN_SWORD, "§6§lFire Demon", 
                "§7Type: MM:FireDemon", "§7Health: 150%", "§7Damage: 175%", "", 
                "§7A blazing inferno boss", "", "§aClick to create"));
            
            gui.setItem(20, createItem(Material.DIAMOND_SWORD, "§9§lFrost Giant", 
                "§7Type: MM:FrostGiant", "§7Health: 400%", "§7Damage: 150%", "", 
                "§7A colossal ice warrior", "", "§aClick to create"));
        }
        
        // Control buttons
        gui.setItem(52, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to boss config", "", "§aClick to go back"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        playSound(player, Sound.UI_BUTTON_CLICK);
        
        String title = event.getView().getTitle();
        if (title.equals("§6§lBoss Configuration")) {
            handleMainMenuClick(player, event.getSlot(), event.getClick());
        } else if (title.startsWith("§6§lEditing Boss: ")) {
            handleBossEditorClick(player, event.getSlot(), event.getClick());
        } else if (title.equals("§6§lBoss Templates")) {
            handleTemplatesClick(player, event.getSlot(), event.getClick());
        }
    }
    
    /**
     * Handle main menu clicks.
     */
    private void handleMainMenuClick(Player player, int slot, ClickType clickType) {
        switch (slot) {
            case 45: // Create New Boss
                startChatInput(player, "create_boss", "§aEnter name for new boss:");
                break;
            case 46: // Import Boss
                player.sendMessage("§eImport feature coming soon!");
                break;
            case 47: // Export All
                exportAllBosses(player);
                break;
            case 48: // Boss Templates
                openBossTemplates(player);
                break;
            case 49: // Reload Config
                reloadBossConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Check if clicking on a boss
                if (slot >= 9 && slot < 45) {
                    ItemStack item = player.getOpenInventory().getItem(slot);
                    if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                        String bossName = item.getItemMeta().getDisplayName().replace("§c§l", "");
                        
                        if (clickType == ClickType.LEFT) {
                            openBossEditor(player, bossName);
                        } else if (clickType == ClickType.RIGHT) {
                            deleteBoss(player, bossName);
                        } else if (clickType == ClickType.SHIFT_LEFT) {
                            duplicateBoss(player, bossName);
                        } else if (clickType == ClickType.MIDDLE) {
                            testSpawnBoss(player, bossName);
                        }
                    }
                }
                break;
        }
    }
    
    /**
     * Handle boss editor clicks.
     */
    private void handleBossEditorClick(Player player, int slot, ClickType clickType) {
        String bossName = editingBoss.get(player.getUniqueId());
        if (bossName == null) return;
        
        switch (slot) {
            case 10: // Boss Type
                openBossTypeSelector(player, bossName);
                break;
            case 11: // Health Multiplier
                adjustHealthMultiplier(player, bossName, clickType);
                break;
            case 12: // Damage Multiplier
                adjustDamageMultiplier(player, bossName, clickType);
                break;
            case 13: // Announce Toggle
                toggleAnnounce(player, bossName);
                break;
            case 19: // Advanced Settings
                player.sendMessage("§eAdvanced settings coming soon!");
                break;
            case 20: // Custom Abilities
                player.sendMessage("§eCustom abilities coming soon!");
                break;
            case 21: // Custom Loot
                openBossLootConfig(player, bossName);
                break;
            case 22: // Rewards
                openBossRewardsConfig(player, bossName);
                break;
            case 28: // Test Spawn
                testSpawnBoss(player, bossName);
                break;
            case 29: // Statistics
                showBossStatistics(player, bossName);
                break;
            case 45: // Rename Boss
                startChatInput(player, "rename_boss", "§aEnter new name for boss:");
                break;
            case 46: // Clone Boss
                cloneBoss(player, bossName);
                break;
            case 49: // Save Changes
                saveBossChanges(player, bossName);
                break;
            case 52: // Back
                openBossConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
        }
    }
    
    /**
     * Handle templates clicks.
     */
    private void handleTemplatesClick(Player player, int slot, ClickType clickType) {
        switch (slot) {
            case 9: // Wither Lord
                createBossFromTemplate(player, "wither_lord", "VANILLA:WITHER", 2.0, 1.5);
                break;
            case 10: // Ender Dragon
                createBossFromTemplate(player, "ender_dragon", "VANILLA:ENDER_DRAGON", 1.0, 1.0);
                break;
            case 11: // Iron Golem Champion
                createBossFromTemplate(player, "iron_champion", "VANILLA:IRON_GOLEM", 3.0, 2.0);
                break;
            case 12: // Zombie King
                createBossFromTemplate(player, "zombie_king", "VANILLA:ZOMBIE", 5.0, 2.5);
                break;
            case 13: // Bone Archer
                createBossFromTemplate(player, "bone_archer", "VANILLA:SKELETON", 2.0, 3.0);
                break;
            case 19: // Fire Demon
                if (plugin.getServer().getPluginManager().getPlugin("MythicMobs") != null) {
                    createBossFromTemplate(player, "fire_demon", "MM:FireDemon", 1.5, 1.75);
                }
                break;
            case 20: // Frost Giant
                if (plugin.getServer().getPluginManager().getPlugin("MythicMobs") != null) {
                    createBossFromTemplate(player, "frost_giant", "MM:FrostGiant", 4.0, 1.5);
                }
                break;
            case 52: // Back
                openBossConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
        }
    }
    
    /**
     * Start chat input for various operations.
     */
    private void startChatInput(Player player, String mode, String prompt) {
        chatInputMode.put(player.getUniqueId(), mode);
        awaitingInput.add(player.getUniqueId());
        player.closeInventory();
        player.sendMessage(prompt);
        player.sendMessage("§7Type 'cancel' to cancel operation.");
    }
    
    /**
     * Handle chat input for various operations.
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        if (!awaitingInput.contains(playerId)) return;
        
        event.setCancelled(true);
        awaitingInput.remove(playerId);
        
        String input = event.getMessage().trim();
        String mode = chatInputMode.remove(playerId);
        
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§cOperation cancelled.");
            Bukkit.getScheduler().runTask(plugin, () -> openBossConfig(player));
            return;
        }
        
        Bukkit.getScheduler().runTask(plugin, () -> {
            switch (mode) {
                case "create_boss":
                    createNewBoss(player, input);
                    break;
                case "rename_boss":
                    renameBoss(player, input);
                    break;
                default:
                    player.sendMessage("§cUnknown input mode.");
                    break;
            }
        });
    }
    
    /**
     * Create a new boss.
     */
    private void createNewBoss(Player player, String bossName) {
        if (bossName.isEmpty() || !bossName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid boss name. Use only letters, numbers, and underscores.");
            openBossConfig(player);
            return;
        }
        
        YamlConfiguration bossConfig = loadBossConfig();
        if (bossConfig.contains("bosses." + bossName)) {
            player.sendMessage("§cBoss '" + bossName + "' already exists!");
            openBossConfig(player);
            return;
        }
        
        // Create default boss
        ConfigurationSection boss = bossConfig.createSection("bosses." + bossName);
        boss.set("type", "VANILLA:WITHER");
        boss.set("healthMultiplier", 1.0);
        boss.set("damageMultiplier", 1.0);
        boss.set("announce", true);
        
        saveBossConfig(bossConfig);
        
        player.sendMessage("§aCreated new boss: " + bossName);
        openBossEditor(player, bossName);
    }
    
    /**
     * Create boss from template.
     */
    private void createBossFromTemplate(Player player, String templateName, String type, double health, double damage) {
        YamlConfiguration bossConfig = loadBossConfig();
        
        String bossName = templateName;
        int counter = 1;
        while (bossConfig.contains("bosses." + bossName)) {
            bossName = templateName + "_" + counter;
            counter++;
        }
        
        ConfigurationSection boss = bossConfig.createSection("bosses." + bossName);
        boss.set("type", type);
        boss.set("healthMultiplier", health);
        boss.set("damageMultiplier", damage);
        boss.set("announce", true);
        
        saveBossConfig(bossConfig);
        
        player.sendMessage("§aCreated boss from template: " + bossName);
        openBossEditor(player, bossName);
    }
    
    /**
     * Delete a boss.
     */
    private void deleteBoss(Player player, String bossName) {
        YamlConfiguration bossConfig = loadBossConfig();
        bossConfig.set("bosses." + bossName, null);
        saveBossConfig(bossConfig);
        
        player.sendMessage("§cDeleted boss: " + bossName);
        openBossConfig(player);
    }
    
    /**
     * Duplicate a boss.
     */
    private void duplicateBoss(Player player, String bossName) {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection originalBoss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        if (originalBoss == null) {
            player.sendMessage("§cBoss not found!");
            return;
        }
        
        String newName = bossName + "_copy";
        int counter = 1;
        while (bossConfig.contains("bosses." + newName)) {
            newName = bossName + "_copy" + counter;
            counter++;
        }
        
        // Copy the boss
        ConfigurationSection newBoss = bossConfig.createSection("bosses." + newName);
        for (String key : originalBoss.getKeys(true)) {
            newBoss.set(key, originalBoss.get(key));
        }
        
        saveBossConfig(bossConfig);
        player.sendMessage("§aDuplicated boss as: " + newName);
        openBossConfig(player);
    }
    
    /**
     * Rename a boss.
     */
    private void renameBoss(Player player, String newName) {
        String oldName = editingBoss.get(player.getUniqueId());
        if (oldName == null) return;
        
        if (newName.isEmpty() || !newName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid boss name. Use only letters, numbers, and underscores.");
            openBossEditor(player, oldName);
            return;
        }
        
        YamlConfiguration bossConfig = loadBossConfig();
        if (bossConfig.contains("bosses." + newName)) {
            player.sendMessage("§cBoss '" + newName + "' already exists!");
            openBossEditor(player, oldName);
            return;
        }
        
        // Copy old boss to new name
        ConfigurationSection oldBoss = bossConfig.getConfigurationSection("bosses." + oldName);
        if (oldBoss != null) {
            ConfigurationSection newBoss = bossConfig.createSection("bosses." + newName);
            for (String key : oldBoss.getKeys(true)) {
                newBoss.set(key, oldBoss.get(key));
            }
            bossConfig.set("bosses." + oldName, null);
            saveBossConfig(bossConfig);
            
            editingBoss.put(player.getUniqueId(), newName);
            player.sendMessage("§aRenamed boss to: " + newName);
            openBossEditor(player, newName);
        }
    }
    
    /**
     * Adjust health multiplier.
     */
    private void adjustHealthMultiplier(Player player, String bossName, ClickType clickType) {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection boss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        if (boss != null) {
            double current = boss.getDouble("healthMultiplier", 1.0);
            double newValue = current;
            
            if (clickType == ClickType.LEFT) {
                newValue = Math.min(10.0, current + 0.1);
            } else if (clickType == ClickType.RIGHT) {
                newValue = Math.max(0.1, current - 0.1);
            } else if (clickType == ClickType.SHIFT_LEFT) {
                startChatInput(player, "set_health", "§aEnter health multiplier (0.1-10.0):");
                return;
            }
            
            boss.set("healthMultiplier", Math.round(newValue * 10.0) / 10.0);
            saveBossConfig(bossConfig);
            player.sendMessage("§aSet health multiplier to: " + (Math.round(newValue * 1000.0) / 10.0) + "%");
        }
        
        openBossEditor(player, bossName);
    }
    
    /**
     * Adjust damage multiplier.
     */
    private void adjustDamageMultiplier(Player player, String bossName, ClickType clickType) {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection boss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        if (boss != null) {
            double current = boss.getDouble("damageMultiplier", 1.0);
            double newValue = current;
            
            if (clickType == ClickType.LEFT) {
                newValue = Math.min(10.0, current + 0.1);
            } else if (clickType == ClickType.RIGHT) {
                newValue = Math.max(0.1, current - 0.1);
            } else if (clickType == ClickType.SHIFT_LEFT) {
                startChatInput(player, "set_damage", "§aEnter damage multiplier (0.1-10.0):");
                return;
            }
            
            boss.set("damageMultiplier", Math.round(newValue * 10.0) / 10.0);
            saveBossConfig(bossConfig);
            player.sendMessage("§aSet damage multiplier to: " + (Math.round(newValue * 1000.0) / 10.0) + "%");
        }
        
        openBossEditor(player, bossName);
    }
    
    /**
     * Toggle announce setting.
     */
    private void toggleAnnounce(Player player, String bossName) {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection boss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        if (boss != null) {
            boolean current = boss.getBoolean("announce", true);
            boss.set("announce", !current);
            saveBossConfig(bossConfig);
            player.sendMessage("§aAnnounce setting: " + (!current ? "§aEnabled" : "§cDisabled"));
        }
        
        openBossEditor(player, bossName);
    }
    
    /**
     * Open boss type selector.
     */
    private void openBossTypeSelector(Player player, String bossName) {
        player.sendMessage("§eBoss type selector coming soon!");
        openBossEditor(player, bossName);
    }
    
    /**
     * Open boss loot configuration.
     */
    private void openBossLootConfig(Player player, String bossName) {
        player.sendMessage("§eBoss loot configuration coming soon!");
        openBossEditor(player, bossName);
    }
    
    /**
     * Open boss rewards configuration.
     */
    private void openBossRewardsConfig(Player player, String bossName) {
        player.sendMessage("§eBoss rewards configuration coming soon!");
        openBossEditor(player, bossName);
    }
    
    /**
     * Test spawn a boss.
     */
    private void testSpawnBoss(Player player, String bossName) {
        player.sendMessage("§eTest spawning boss: " + bossName);
        player.sendMessage("§cThis feature will spawn a real boss mob!");
        player.sendMessage("§7Feature coming soon...");
    }
    
    /**
     * Show boss statistics.
     */
    private void showBossStatistics(Player player, String bossName) {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection boss = bossConfig.getConfigurationSection("bosses." + bossName);
        
        if (boss != null) {
            String type = boss.getString("type", "VANILLA:WITHER");
            double healthMult = boss.getDouble("healthMultiplier", 1.0);
            double damageMult = boss.getDouble("damageMultiplier", 1.0);
            boolean announce = boss.getBoolean("announce", true);
            
            player.sendMessage("§6§l=== Boss Statistics: " + bossName + " ===");
            player.sendMessage("§7Type: §e" + type);
            player.sendMessage("§7Health Multiplier: §c" + (healthMult * 100) + "%");
            player.sendMessage("§7Damage Multiplier: §6" + (damageMult * 100) + "%");
            player.sendMessage("§7Announce Spawn: " + (announce ? "§aYes" : "§cNo"));
            player.sendMessage("§6§l================================");
        }
        
        openBossEditor(player, bossName);
    }
    
    /**
     * Clone a boss.
     */
    private void cloneBoss(Player player, String bossName) {
        duplicateBoss(player, bossName);
    }
    
    /**
     * Save boss changes.
     */
    private void saveBossChanges(Player player, String bossName) {
        player.sendMessage("§aAll changes have been saved automatically!");
        openBossEditor(player, bossName);
    }
    
    /**
     * Export all bosses to a file.
     */
    private void exportAllBosses(Player player) {
        player.sendMessage("§eExport feature coming soon!");
    }
    
    /**
     * Reload boss configuration.
     */
    private void reloadBossConfig(Player player) {
        plugin.getConfigManager().loadConfigurations();
        player.sendMessage("§aBoss configuration reloaded!");
        openBossConfig(player);
    }
    
    /**
     * Load boss configuration.
     */
    private YamlConfiguration loadBossConfig() {
        File bossFile = new File(plugin.getDataFolder(), "bosses.yml");
        if (!bossFile.exists()) {
            plugin.saveResource("bosses.yml", false);
        }
        return YamlConfiguration.loadConfiguration(bossFile);
    }
    
    /**
     * Save boss configuration.
     */
    private void saveBossConfig(YamlConfiguration config) {
        try {
            File bossFile = new File(plugin.getDataFolder(), "bosses.yml");
            config.save(bossFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save boss configuration: " + e.getMessage());
        }
    }
    
    /**
     * Get all boss names.
     */
    private Set<String> getBosses() {
        YamlConfiguration bossConfig = loadBossConfig();
        ConfigurationSection bosses = bossConfig.getConfigurationSection("bosses");
        return bosses != null ? bosses.getKeys(false) : new HashSet<>();
    }
    
    /**
     * Get icon material for a boss based on its name and type.
     */
    private Material getBossIcon(String bossName, String type) {
        String lowerType = type.toLowerCase();
        String lowerName = bossName.toLowerCase();
        
        if (lowerType.contains("wither") || lowerName.contains("wither")) {
            return Material.WITHER_SKELETON_SKULL;
        } else if (lowerType.contains("dragon") || lowerName.contains("dragon")) {
            return Material.DRAGON_HEAD;
        } else if (lowerType.contains("golem") || lowerName.contains("golem")) {
            return Material.IRON_BLOCK;
        } else if (lowerType.contains("zombie") || lowerName.contains("zombie")) {
            return Material.ZOMBIE_HEAD;
        } else if (lowerType.contains("skeleton") || lowerName.contains("skeleton")) {
            return Material.SKELETON_SKULL;
        } else if (lowerType.contains("creeper") || lowerName.contains("creeper")) {
            return Material.CREEPER_HEAD;
        } else if (lowerType.startsWith("mm:") || lowerName.contains("mythic")) {
            return Material.DRAGON_HEAD;
        } else if (lowerName.contains("fire") || lowerName.contains("flame")) {
            return Material.FIRE_CHARGE;
        } else if (lowerName.contains("ice") || lowerName.contains("frost")) {
            return Material.ICE;
        } else {
            return Material.WITHER_SKELETON_SKULL;
        }
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Play sound for player if enabled.
     */
    private void playSound(Player player, Sound sound) {
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
        }
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
        }
    }
    
    /**
     * Check if a player has a boss config GUI open.
     */
    public boolean hasBossConfigGUIOpen(Player player) {
        return openGuis.containsKey(player.getUniqueId());
    }
    
    /**
     * Close boss config GUI for a player.
     */
    public void closeBossConfigGUI(Player player) {
        if (openGuis.containsKey(player.getUniqueId())) {
            player.closeInventory();
        }
    }
}
