package com.apexdungeons.commands;

import com.apexdungeons.DungeonX;
import com.apexdungeons.gui.ToolsGUI;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Main command handler for DungeonX - GUI-driven workflow with minimal chat commands.
 * Primary interface is through GUIs, commands are mainly for quick access.
 */
public class DungeonCommand implements CommandExecutor {

    private final DungeonX plugin;

    public DungeonCommand(DungeonX plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cDungeonX can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        // No arguments - open main tools GUI
        if (args.length == 0) {
            if (player.hasPermission("dungeonx.admin")) {
                new ToolsGUI(plugin).openToolsGUI(player);
            } else {
                // Regular players get instance browser
                player.sendMessage("§eDungeonX - Commercial-quality dungeon system");
                player.sendMessage("§7Contact an admin to access dungeon tools.");
            }
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "tools":
            case "admin":
                if (!player.hasPermission("dungeonx.admin")) {
                    player.sendMessage("§cYou don't have permission to access DungeonX tools.");
                    return true;
                }
                new ToolsGUI(plugin).openToolsGUI(player);
                break;

            case "stats":
                showSystemStats(player);
                break;

            case "reload":
                if (!player.hasPermission("dungeonx.admin")) {
                    player.sendMessage("§cYou don't have permission to reload DungeonX.");
                    return true;
                }
                reloadSystem(player);
                break;

            case "cleanup":
                if (!player.hasPermission("dungeonx.admin")) {
                    player.sendMessage("§cYou don't have permission to run cleanup.");
                    return true;
                }
                runCleanup(player);
                break;

            case "version":
            case "info":
                showVersionInfo(player);
                break;

            case "help":
            default:
                sendHelpMessage(player);
                break;
        }

        return true;
    }

    /**
     * Show system statistics.
     */
    private void showSystemStats(Player player) {
        player.sendMessage("§6§l=== DungeonX Statistics ===");
        player.sendMessage("§eTemplates: §a" + plugin.getTemplateManager().getTemplateCount());
        player.sendMessage("§eActive Instances: §a" + plugin.getInstanceManager().getInstanceCount());
        player.sendMessage("§ePublished Templates: §a" + plugin.getTemplateManager().getPublishedTemplates().size());
        
        if (player.hasPermission("dungeonx.admin")) {
            player.sendMessage("§eDatastore: §a" + plugin.getDataStore().getStats());
            player.sendMessage("§eMythicMobs: §a" + (plugin.getMobAdapter() != null ? "Available" : "Unavailable"));
        }
    }

    /**
     * Reload the system.
     */
    private void reloadSystem(Player player) {
        try {
            plugin.getConfigManager().reloadConfigurations();
            plugin.getDataStore().saveAll();
            player.sendMessage("§aDungeonX reloaded successfully!");
        } catch (Exception e) {
            player.sendMessage("§cFailed to reload DungeonX: " + e.getMessage());
            plugin.getLogger().severe("Reload failed: " + e.getMessage());
        }
    }

    /**
     * Run cleanup operations.
     */
    private void runCleanup(Player player) {
        player.sendMessage("§eRunning DungeonX cleanup...");
        
        // Cleanup expired instances
        plugin.getInstanceManager().cleanupExpiredInstances();
        
        // Force save datastore
        plugin.getDataStore().forceSave();
        
        player.sendMessage("§aCleanup completed!");
    }

    /**
     * Show version and system information.
     */
    private void showVersionInfo(Player player) {
        player.sendMessage("§6§l=== DungeonX Information ===");
        player.sendMessage("§eVersion: §a" + plugin.getDescription().getVersion());
        player.sendMessage("§eAuthor: §aVexy");
        player.sendMessage("§eAPI Version: §a" + plugin.getDescription().getAPIVersion());
        player.sendMessage("§eWebsite: §b" + plugin.getDescription().getWebsite());
        player.sendMessage("");
        player.sendMessage("§7Commercial-quality dungeon system for Paper 1.21+");
        player.sendMessage("§7Features true instancing, reliable saving, and professional tools.");
    }

    /**
     * Send help message with available commands.
     */
    private void sendHelpMessage(Player player) {
        player.sendMessage("§6§l=== DungeonX Help ===");
        player.sendMessage("§eDungeonX uses a GUI-driven workflow. Most actions are performed through menus.");
        player.sendMessage("");
        player.sendMessage("§e/dgn §7- Open main tools (admin) or info (players)");
        player.sendMessage("§e/dgn help §7- Show this help message");
        player.sendMessage("§e/dgn version §7- Show version information");
        player.sendMessage("§e/dgn stats §7- Show system statistics");
        
        if (player.hasPermission("dungeonx.admin")) {
            player.sendMessage("");
            player.sendMessage("§6Admin Commands:");
            player.sendMessage("§e/dgn tools §7- Open DungeonX tools GUI");
            player.sendMessage("§e/dgn reload §7- Reload configuration");
            player.sendMessage("§e/dgn cleanup §7- Run cleanup operations");
        }
        
        player.sendMessage("");
        player.sendMessage("§7Use the GUI tools for template creation, instance management,");
        player.sendMessage("§7schematic library, and all building operations.");
    }
}
