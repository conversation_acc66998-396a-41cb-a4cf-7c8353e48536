package com.apexdungeons.gui;

import com.apexdungeons.DungeonXMinimal;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Advanced GUI for managing mob spawn pools with full CRUD operations.
 * Supports creating, editing, and deleting mob pools and individual mobs.
 */
public class MobConfigGUI implements Listener {
    
    private final DungeonXMinimal plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    private final Map<UUID, String> editingPool = new HashMap<>();
    private final Map<UUID, String> chatInputMode = new HashMap<>();
    private final Set<UUID> awaitingInput = new HashSet<>();
    
    public MobConfigGUI(DungeonXMinimal plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the main mob configuration GUI.
     */
    public void openMobConfig(Player player) {
        if (!player.hasPermission("dungeonx.admin")) {
            player.sendMessage("§cYou don't have permission to configure mob pools.");
            return;
        }
        
        Inventory gui = createMobConfigInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create the main mob configuration inventory.
     */
    private Inventory createMobConfigInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lMob Pool Configuration");
        
        // Header
        gui.setItem(4, createItem(Material.SPAWNER, "§e§lMob Pool Manager", 
            "§7Manage all mob spawn pools for dungeons", 
            "§7Create, edit, and delete mob pools", "", 
            "§aTotal Pools: §e" + getMobPools().size()));
        
        // Load existing mob pools
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pools = mobConfig.getConfigurationSection("pools");
        
        int slot = 9;
        if (pools != null) {
            for (String poolName : pools.getKeys(false)) {
                if (slot >= 45) break; // Leave space for controls
                
                ConfigurationSection pool = pools.getConfigurationSection(poolName);
                List<String> lore = new ArrayList<>();
                lore.add("§7Pool: §e" + poolName);
                lore.add("§7Mobs: §a" + (pool != null ? pool.getKeys(false).size() : 0));
                lore.add("");
                
                // Show first few mobs in the pool
                if (pool != null) {
                    int count = 0;
                    for (String key : pool.getKeys(false)) {
                        if (count >= 3) {
                            lore.add("§7... and " + (pool.getKeys(false).size() - 3) + " more");
                            break;
                        }
                        ConfigurationSection mob = pool.getConfigurationSection(key);
                        if (mob != null) {
                            String type = mob.getString("type", "VANILLA:ZOMBIE");
                            int weight = mob.getInt("weight", 1);
                            lore.add("§7- " + type + " §8(weight: " + weight + ")");
                        }
                        count++;
                    }
                }
                
                lore.add("");
                lore.add("§eLeft-click: §7Edit pool");
                lore.add("§eRight-click: §7Delete pool");
                lore.add("§eShift-click: §7Duplicate pool");
                
                Material icon = getPoolIcon(poolName);
                gui.setItem(slot, createItem(icon, "§a§l" + poolName, lore.toArray(new String[0])));
                slot++;
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "§a§lCreate New Pool", 
            "§7Create a new mob spawn pool", "", "§aClick to create"));
        
        gui.setItem(46, createItem(Material.BOOK, "§e§lImport Pool", 
            "§7Import mob pool from file", "", "§aClick to import"));
        
        gui.setItem(47, createItem(Material.WRITABLE_BOOK, "§6§lExport All", 
            "§7Export all pools to file", "", "§aClick to export"));
        
        gui.setItem(48, createItem(Material.ZOMBIE_HEAD, "§d§lMob Browser", 
            "§7Browse available mob types", "", "§aClick to browse"));
        
        gui.setItem(49, createItem(Material.REDSTONE, "§c§lReload Config", 
            "§7Reload mob configuration", "", "§aClick to reload"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Open pool editor for specific mob pool.
     */
    public void openPoolEditor(Player player, String poolName) {
        editingPool.put(player.getUniqueId(), poolName);
        
        Inventory gui = createPoolEditorInventory(player, poolName);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create pool editor inventory.
     */
    private Inventory createPoolEditorInventory(Player player, String poolName) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lEditing Pool: " + poolName);
        
        // Header
        gui.setItem(4, createItem(Material.SPAWNER, "§e§lPool: " + poolName, 
            "§7Editing mob spawn pool", "", "§7Add, edit, or remove mobs"));
        
        // Load pool mobs
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pool = mobConfig.getConfigurationSection("pools." + poolName);
        
        int slot = 9;
        if (pool != null) {
            for (String key : pool.getKeys(false)) {
                if (slot >= 45) break;
                
                ConfigurationSection mob = pool.getConfigurationSection(key);
                if (mob != null) {
                    String mobType = mob.getString("type", "VANILLA:ZOMBIE");
                    int weight = mob.getInt("weight", 1);
                    
                    Material material = getMobMaterial(mobType);
                    
                    List<String> lore = new ArrayList<>();
                    lore.add("§7Type: §e" + mobType);
                    lore.add("§7Weight: §b" + weight);
                    lore.add("");
                    
                    // Add mob-specific info
                    if (mobType.startsWith("VANILLA:")) {
                        lore.add("§7Vanilla Minecraft mob");
                    } else if (mobType.startsWith("MM:")) {
                        lore.add("§7MythicMobs creature");
                        lore.add("§7Requires MythicMobs plugin");
                    }
                    
                    lore.add("");
                    lore.add("§eLeft-click: §7Edit mob");
                    lore.add("§eRight-click: §7Delete mob");
                    lore.add("§eShift-click: §7Duplicate mob");
                    
                    gui.setItem(slot, createItem(material, "§a§lMob #" + (slot - 8), lore.toArray(new String[0])));
                }
                slot++;
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "§a§lAdd Mob", 
            "§7Add new mob to pool", "", "§aClick to add"));
        
        gui.setItem(46, createItem(Material.COMPARATOR, "§e§lPool Settings", 
            "§7Configure pool settings", "", "§aClick to configure"));
        
        gui.setItem(47, createItem(Material.CHEST_MINECART, "§6§lTest Pool", 
            "§7Test mob spawning", "", "§aClick to test"));
        
        gui.setItem(49, createItem(Material.PAPER, "§b§lRename Pool", 
            "§7Rename this pool", "", "§aClick to rename"));
        
        gui.setItem(52, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to main menu", "", "§aClick to go back"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Open mob browser GUI.
     */
    public void openMobBrowser(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lMob Type Browser");
        
        // Header
        gui.setItem(4, createItem(Material.ZOMBIE_HEAD, "§e§lAvailable Mob Types", 
            "§7Browse all available mob types", "", "§7Click on a mob to see details"));
        
        // Vanilla mobs section
        gui.setItem(9, createItem(Material.ZOMBIE_HEAD, "§a§lVanilla Mobs", 
            "§7Standard Minecraft mobs", "", "§aClick to browse"));
        
        gui.setItem(10, createItem(Material.SKELETON_SKULL, "§7Skeleton", 
            "§7Type: VANILLA:SKELETON", "§7Common ranged mob", "", "§eClick for details"));
        
        gui.setItem(11, createItem(Material.ZOMBIE_HEAD, "§7Zombie", 
            "§7Type: VANILLA:ZOMBIE", "§7Common melee mob", "", "§eClick for details"));
        
        gui.setItem(12, createItem(Material.CREEPER_HEAD, "§7Creeper", 
            "§7Type: VANILLA:CREEPER", "§7Explosive mob", "", "§eClick for details"));
        
        gui.setItem(13, createItem(Material.SPIDER_EYE, "§7Spider", 
            "§7Type: VANILLA:SPIDER", "§7Fast climbing mob", "", "§eClick for details"));
        
        gui.setItem(14, createItem(Material.WITHER_SKELETON_SKULL, "§7Wither Skeleton", 
            "§7Type: VANILLA:WITHER_SKELETON", "§7Strong nether mob", "", "§eClick for details"));
        
        // MythicMobs section
        gui.setItem(18, createItem(Material.DRAGON_HEAD, "§d§lMythicMobs", 
            "§7Custom MythicMobs creatures", "§7Requires MythicMobs plugin", "", "§aClick to browse"));
        
        if (plugin.getServer().getPluginManager().getPlugin("MythicMobs") != null) {
            gui.setItem(19, createItem(Material.DIAMOND_SWORD, "§7Custom Boss", 
                "§7Type: MM:CustomBoss", "§7Example MythicMob", "", "§eClick for details"));
            
            gui.setItem(20, createItem(Material.GOLDEN_SWORD, "§7Elite Warrior", 
                "§7Type: MM:EliteWarrior", "§7Example MythicMob", "", "§eClick for details"));
        } else {
            gui.setItem(19, createItem(Material.BARRIER, "§c§lMythicMobs Not Found", 
                "§7Install MythicMobs to use", "§7custom mob types", "", "§cPlugin required"));
        }
        
        // Control buttons
        gui.setItem(52, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to mob config", "", "§aClick to go back"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        playSound(player, Sound.UI_BUTTON_CLICK);
        
        String title = event.getView().getTitle();
        if (title.equals("§6§lMob Pool Configuration")) {
            handleMainMenuClick(player, event.getSlot(), event.getClick());
        } else if (title.startsWith("§6§lEditing Pool: ")) {
            handlePoolEditorClick(player, event.getSlot(), event.getClick());
        } else if (title.equals("§6§lMob Type Browser")) {
            handleMobBrowserClick(player, event.getSlot(), event.getClick());
        }
    }
    
    /**
     * Handle main menu clicks.
     */
    private void handleMainMenuClick(Player player, int slot, ClickType clickType) {
        switch (slot) {
            case 45: // Create New Pool
                startChatInput(player, "create_pool", "§aEnter name for new mob pool:");
                break;
            case 46: // Import Pool
                player.sendMessage("§eImport feature coming soon!");
                break;
            case 47: // Export All
                exportAllPools(player);
                break;
            case 48: // Mob Browser
                openMobBrowser(player);
                break;
            case 49: // Reload Config
                reloadMobConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Check if clicking on a pool
                if (slot >= 9 && slot < 45) {
                    ItemStack item = player.getOpenInventory().getItem(slot);
                    if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                        String poolName = item.getItemMeta().getDisplayName().replace("§a§l", "");
                        
                        if (clickType == ClickType.LEFT) {
                            openPoolEditor(player, poolName);
                        } else if (clickType == ClickType.RIGHT) {
                            deletePool(player, poolName);
                        } else if (clickType == ClickType.SHIFT_LEFT) {
                            duplicatePool(player, poolName);
                        }
                    }
                }
                break;
        }
    }
    
    /**
     * Handle pool editor clicks.
     */
    private void handlePoolEditorClick(Player player, int slot, ClickType clickType) {
        String poolName = editingPool.get(player.getUniqueId());
        if (poolName == null) return;
        
        switch (slot) {
            case 45: // Add Mob
                startMobCreation(player, poolName);
                break;
            case 46: // Pool Settings
                openPoolSettings(player, poolName);
                break;
            case 47: // Test Pool
                testPool(player, poolName);
                break;
            case 49: // Rename Pool
                startChatInput(player, "rename_pool", "§aEnter new name for pool:");
                break;
            case 52: // Back
                openMobConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Handle mob clicks
                if (slot >= 9 && slot < 45) {
                    handleMobClick(player, slot, clickType, poolName);
                }
                break;
        }
    }
    
    /**
     * Handle mob browser clicks.
     */
    private void handleMobBrowserClick(Player player, int slot, ClickType clickType) {
        switch (slot) {
            case 52: // Back
                openMobConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Handle mob type clicks for details
                if (slot >= 10 && slot <= 20) {
                    showMobDetails(player, slot);
                }
                break;
        }
    }
    
    /**
     * Handle mob clicks in pool editor.
     */
    private void handleMobClick(Player player, int slot, ClickType clickType, String poolName) {
        if (clickType == ClickType.LEFT) {
            // Edit mob
            startMobEdit(player, poolName, slot);
        } else if (clickType == ClickType.RIGHT) {
            // Delete mob
            deleteMob(player, poolName, slot);
        } else if (clickType == ClickType.SHIFT_LEFT) {
            // Duplicate mob
            duplicateMob(player, poolName, slot);
        }
    }
    
    /**
     * Start chat input for various operations.
     */
    private void startChatInput(Player player, String mode, String prompt) {
        chatInputMode.put(player.getUniqueId(), mode);
        awaitingInput.add(player.getUniqueId());
        player.closeInventory();
        player.sendMessage(prompt);
        player.sendMessage("§7Type 'cancel' to cancel operation.");
    }
    
    /**
     * Handle chat input for various operations.
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        if (!awaitingInput.contains(playerId)) return;
        
        event.setCancelled(true);
        awaitingInput.remove(playerId);
        
        String input = event.getMessage().trim();
        String mode = chatInputMode.remove(playerId);
        
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§cOperation cancelled.");
            Bukkit.getScheduler().runTask(plugin, () -> openMobConfig(player));
            return;
        }
        
        Bukkit.getScheduler().runTask(plugin, () -> {
            switch (mode) {
                case "create_pool":
                    createNewPool(player, input);
                    break;
                case "rename_pool":
                    renamePool(player, input);
                    break;
                default:
                    player.sendMessage("§cUnknown input mode.");
                    break;
            }
        });
    }
    
    /**
     * Create a new mob pool.
     */
    private void createNewPool(Player player, String poolName) {
        if (poolName.isEmpty() || !poolName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid pool name. Use only letters, numbers, and underscores.");
            openMobConfig(player);
            return;
        }
        
        YamlConfiguration mobConfig = loadMobConfig();
        if (mobConfig.contains("pools." + poolName)) {
            player.sendMessage("§cPool '" + poolName + "' already exists!");
            openMobConfig(player);
            return;
        }
        
        // Create empty pool
        mobConfig.createSection("pools." + poolName);
        saveMobConfig(mobConfig);
        
        player.sendMessage("§aCreated new mob pool: " + poolName);
        openPoolEditor(player, poolName);
    }
    
    /**
     * Delete a mob pool.
     */
    private void deletePool(Player player, String poolName) {
        YamlConfiguration mobConfig = loadMobConfig();
        mobConfig.set("pools." + poolName, null);
        saveMobConfig(mobConfig);
        
        player.sendMessage("§cDeleted mob pool: " + poolName);
        openMobConfig(player);
    }
    
    /**
     * Duplicate a mob pool.
     */
    private void duplicatePool(Player player, String poolName) {
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection originalPool = mobConfig.getConfigurationSection("pools." + poolName);
        
        if (originalPool == null) {
            player.sendMessage("§cPool not found!");
            return;
        }
        
        String newName = poolName + "_copy";
        int counter = 1;
        while (mobConfig.contains("pools." + newName)) {
            newName = poolName + "_copy" + counter;
            counter++;
        }
        
        // Copy the pool
        ConfigurationSection newPool = mobConfig.createSection("pools." + newName);
        for (String key : originalPool.getKeys(true)) {
            newPool.set(key, originalPool.get(key));
        }
        
        saveMobConfig(mobConfig);
        player.sendMessage("§aDuplicated pool as: " + newName);
        openMobConfig(player);
    }
    
    /**
     * Rename a mob pool.
     */
    private void renamePool(Player player, String newName) {
        String oldName = editingPool.get(player.getUniqueId());
        if (oldName == null) return;
        
        if (newName.isEmpty() || !newName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid pool name. Use only letters, numbers, and underscores.");
            openPoolEditor(player, oldName);
            return;
        }
        
        YamlConfiguration mobConfig = loadMobConfig();
        if (mobConfig.contains("pools." + newName)) {
            player.sendMessage("§cPool '" + newName + "' already exists!");
            openPoolEditor(player, oldName);
            return;
        }
        
        // Copy old pool to new name
        ConfigurationSection oldPool = mobConfig.getConfigurationSection("pools." + oldName);
        if (oldPool != null) {
            ConfigurationSection newPool = mobConfig.createSection("pools." + newName);
            for (String key : oldPool.getKeys(true)) {
                newPool.set(key, oldPool.get(key));
            }
            mobConfig.set("pools." + oldName, null);
            saveMobConfig(mobConfig);
            
            editingPool.put(player.getUniqueId(), newName);
            player.sendMessage("§aRenamed pool to: " + newName);
            openPoolEditor(player, newName);
        }
    }
    
    /**
     * Start mob creation process.
     */
    private void startMobCreation(Player player, String poolName) {
        player.sendMessage("§eMob creation GUI coming soon!");
        openPoolEditor(player, poolName);
    }
    
    /**
     * Start mob editing process.
     */
    private void startMobEdit(Player player, String poolName, int slot) {
        player.sendMessage("§eMob editing GUI coming soon!");
        openPoolEditor(player, poolName);
    }
    
    /**
     * Delete a mob from the pool.
     */
    private void deleteMob(Player player, String poolName, int slot) {
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pool = mobConfig.getConfigurationSection("pools." + poolName);
        
        if (pool != null) {
            List<String> keys = new ArrayList<>(pool.getKeys(false));
            int mobIndex = slot - 9;
            if (mobIndex >= 0 && mobIndex < keys.size()) {
                String keyToDelete = keys.get(mobIndex);
                pool.set(keyToDelete, null);
                saveMobConfig(mobConfig);
                player.sendMessage("§cDeleted mob from pool.");
            }
        }
        
        openPoolEditor(player, poolName);
    }
    
    /**
     * Duplicate a mob in the pool.
     */
    private void duplicateMob(Player player, String poolName, int slot) {
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pool = mobConfig.getConfigurationSection("pools." + poolName);
        
        if (pool != null) {
            List<String> keys = new ArrayList<>(pool.getKeys(false));
            int mobIndex = slot - 9;
            if (mobIndex >= 0 && mobIndex < keys.size()) {
                String originalKey = keys.get(mobIndex);
                ConfigurationSection originalMob = pool.getConfigurationSection(originalKey);
                
                if (originalMob != null) {
                    // Find a new key name
                    String newKey = originalKey + "_copy";
                    int counter = 1;
                    while (pool.contains(newKey)) {
                        newKey = originalKey + "_copy" + counter;
                        counter++;
                    }
                    
                    // Copy the mob
                    ConfigurationSection newMob = pool.createSection(newKey);
                    for (String subKey : originalMob.getKeys(true)) {
                        newMob.set(subKey, originalMob.get(subKey));
                    }
                    
                    saveMobConfig(mobConfig);
                    player.sendMessage("§aDuplicated mob in pool.");
                }
            }
        }
        
        openPoolEditor(player, poolName);
    }
    
    /**
     * Open pool settings GUI.
     */
    private void openPoolSettings(Player player, String poolName) {
        player.sendMessage("§ePool settings GUI coming soon!");
        openPoolEditor(player, poolName);
    }
    
    /**
     * Test a mob pool by showing sample spawns.
     */
    private void testPool(Player player, String poolName) {
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pool = mobConfig.getConfigurationSection("pools." + poolName);
        
        if (pool == null || pool.getKeys(false).isEmpty()) {
            player.sendMessage("§cPool is empty or doesn't exist!");
            return;
        }
        
        player.sendMessage("§aGenerating test spawns from pool: " + poolName);
        
        // Generate 5 random mobs from the pool
        for (int i = 0; i < 5; i++) {
            String randomMob = getRandomMobFromPool(pool);
            if (randomMob != null) {
                player.sendMessage("§7- " + randomMob);
            }
        }
        
        openPoolEditor(player, poolName);
    }
    
    /**
     * Get a random mob from a pool based on weights.
     */
    private String getRandomMobFromPool(ConfigurationSection pool) {
        List<String> mobs = new ArrayList<>();
        List<Integer> weights = new ArrayList<>();
        int totalWeight = 0;
        
        for (String key : pool.getKeys(false)) {
            ConfigurationSection mob = pool.getConfigurationSection(key);
            if (mob != null) {
                String mobType = mob.getString("type", "VANILLA:ZOMBIE");
                int weight = mob.getInt("weight", 1);
                
                mobs.add(mobType);
                weights.add(weight);
                totalWeight += weight;
            }
        }
        
        if (mobs.isEmpty()) return null;
        
        Random random = new Random();
        int randomWeight = random.nextInt(totalWeight);
        int currentWeight = 0;
        
        for (int i = 0; i < mobs.size(); i++) {
            currentWeight += weights.get(i);
            if (randomWeight < currentWeight) {
                return mobs.get(i);
            }
        }
        
        return mobs.get(0); // Fallback
    }
    
    /**
     * Show mob details.
     */
    private void showMobDetails(Player player, int slot) {
        String mobType = "";
        switch (slot) {
            case 10:
                mobType = "VANILLA:SKELETON";
                break;
            case 11:
                mobType = "VANILLA:ZOMBIE";
                break;
            case 12:
                mobType = "VANILLA:CREEPER";
                break;
            case 13:
                mobType = "VANILLA:SPIDER";
                break;
            case 14:
                mobType = "VANILLA:WITHER_SKELETON";
                break;
            default:
                mobType = "Unknown";
                break;
        }
        
        player.sendMessage("§aMob Details: " + mobType);
        player.sendMessage("§7This feature will be expanded in future updates.");
    }
    
    /**
     * Export all mob pools to a file.
     */
    private void exportAllPools(Player player) {
        player.sendMessage("§eExport feature coming soon!");
    }
    
    /**
     * Reload mob configuration.
     */
    private void reloadMobConfig(Player player) {
        plugin.getConfigManager().loadConfigurations();
        player.sendMessage("§aMob configuration reloaded!");
        openMobConfig(player);
    }
    
    /**
     * Load mob configuration.
     */
    private YamlConfiguration loadMobConfig() {
        File mobFile = new File(plugin.getDataFolder(), "mobs.yml");
        if (!mobFile.exists()) {
            plugin.saveResource("mobs.yml", false);
        }
        return YamlConfiguration.loadConfiguration(mobFile);
    }
    
    /**
     * Save mob configuration.
     */
    private void saveMobConfig(YamlConfiguration config) {
        try {
            File mobFile = new File(plugin.getDataFolder(), "mobs.yml");
            config.save(mobFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save mob configuration: " + e.getMessage());
        }
    }
    
    /**
     * Get all mob pool names.
     */
    private Set<String> getMobPools() {
        YamlConfiguration mobConfig = loadMobConfig();
        ConfigurationSection pools = mobConfig.getConfigurationSection("pools");
        return pools != null ? pools.getKeys(false) : new HashSet<>();
    }
    
    /**
     * Get icon material for a pool based on its name.
     */
    private Material getPoolIcon(String poolName) {
        String lowerName = poolName.toLowerCase();
        if (lowerName.contains("crypt") || lowerName.contains("skeleton")) {
            return Material.SKELETON_SKULL;
        } else if (lowerName.contains("zombie") || lowerName.contains("undead")) {
            return Material.ZOMBIE_HEAD;
        } else if (lowerName.contains("spider") || lowerName.contains("web")) {
            return Material.SPIDER_EYE;
        } else if (lowerName.contains("nether") || lowerName.contains("fire")) {
            return Material.WITHER_SKELETON_SKULL;
        } else if (lowerName.contains("boss") || lowerName.contains("elite")) {
            return Material.DRAGON_HEAD;
        } else {
            return Material.SPAWNER;
        }
    }
    
    /**
     * Get material for mob type.
     */
    private Material getMobMaterial(String mobType) {
        String lowerType = mobType.toLowerCase();
        if (lowerType.contains("skeleton")) {
            return Material.SKELETON_SKULL;
        } else if (lowerType.contains("zombie")) {
            return Material.ZOMBIE_HEAD;
        } else if (lowerType.contains("creeper")) {
            return Material.CREEPER_HEAD;
        } else if (lowerType.contains("spider")) {
            return Material.SPIDER_EYE;
        } else if (lowerType.contains("wither")) {
            return Material.WITHER_SKELETON_SKULL;
        } else if (lowerType.startsWith("mm:")) {
            return Material.DRAGON_HEAD;
        } else {
            return Material.SPAWNER;
        }
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Play sound for player if enabled.
     */
    private void playSound(Player player, Sound sound) {
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
        }
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
        }
    }
    
    /**
     * Check if a player has a mob config GUI open.
     */
    public boolean hasMobConfigGUIOpen(Player player) {
        return openGuis.containsKey(player.getUniqueId());
    }
    
    /**
     * Close mob config GUI for a player.
     */
    public void closeMobConfigGUI(Player player) {
        if (openGuis.containsKey(player.getUniqueId())) {
            player.closeInventory();
        }
    }
}
