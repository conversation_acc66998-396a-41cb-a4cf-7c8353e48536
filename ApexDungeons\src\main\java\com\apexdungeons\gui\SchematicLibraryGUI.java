package com.apexdungeons.gui;

import com.apexdungeons.DungeonXMinimal;
import com.apexdungeons.schematics.SchematicFile;
import com.apexdungeons.schematics.SchematicPreview;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.util.*;

/**
 * Schematic Library GUI for browsing and managing schematic files.
 * Supports 3D preview, rotation controls, and drag-and-drop functionality.
 */
public class SchematicLibraryGUI implements Listener {
    
    private final DungeonXMinimal plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    private final Map<UUID, Integer> currentPage = new HashMap<>();
    private final Map<UUID, String> currentCategory = new HashMap<>();
    
    private static final int ITEMS_PER_PAGE = 28; // 4 rows of 7 items
    
    public SchematicLibraryGUI(DungeonXMinimal plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the schematic library for a player.
     */
    public void openSchematicLibrary(Player player) {
        currentPage.put(player.getUniqueId(), 0);
        currentCategory.put(player.getUniqueId(), "all");
        
        Inventory gui = createSchematicLibraryInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create the schematic library inventory.
     */
    private Inventory createSchematicLibraryInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§d§lSchematic Library");
        
        // Get schematics
        List<SchematicFile> schematics = getSchematicsForCategory(currentCategory.get(player.getUniqueId()));
        int page = currentPage.get(player.getUniqueId());
        int startIndex = page * ITEMS_PER_PAGE;
        
        // Category filters (top row)
        gui.setItem(1, createItem(Material.BOOKSHELF, "§e§lAll Schematics", 
            "§7Show all available schematics", "", "§aClick to filter"));
        
        gui.setItem(2, createItem(Material.STONE_BRICKS, "§7§lStructures", 
            "§7Buildings and structures", "", "§aClick to filter"));
        
        gui.setItem(3, createItem(Material.GRASS_BLOCK, "§a§lRooms", 
            "§7Individual dungeon rooms", "", "§aClick to filter"));
        
        gui.setItem(4, createItem(Material.REDSTONE_BLOCK, "§c§lTraps", 
            "§7Traps and mechanisms", "", "§aClick to filter"));
        
        gui.setItem(5, createItem(Material.SPAWNER, "§6§lArenas", 
            "§7Boss arenas and combat areas", "", "§aClick to filter"));
        
        gui.setItem(6, createItem(Material.CHEST, "§e§lTreasure", 
            "§7Treasure rooms and vaults", "", "§aClick to filter"));
        
        gui.setItem(7, createItem(Material.NETHER_PORTAL, "§5§lPortals", 
            "§7Portals and gateways", "", "§aClick to filter"));
        
        // Schematic items (4 rows, 7 columns each)
        int slot = 10;
        for (int i = 0; i < ITEMS_PER_PAGE && (startIndex + i) < schematics.size(); i++) {
            SchematicFile schematic = schematics.get(startIndex + i);
            gui.setItem(slot, createSchematicItem(schematic));
            
            slot++;
            if (slot % 9 == 8) { // Skip last column
                slot += 2;
            }
        }
        
        // Navigation controls (bottom row)
        if (page > 0) {
            gui.setItem(45, createItem(Material.ARROW, "§a§lPrevious Page", 
                "§7Go to page " + page, "", "§aClick to navigate"));
        }
        
        int totalPages = (int) Math.ceil((double) schematics.size() / ITEMS_PER_PAGE);
        gui.setItem(49, createItem(Material.BOOK, "§e§lPage " + (page + 1) + "/" + totalPages, 
            "§7Showing " + Math.min(ITEMS_PER_PAGE, schematics.size() - startIndex) + " of " + schematics.size() + " schematics", 
            "", "§7Category: §e" + currentCategory.get(player.getUniqueId())));
        
        if (page < totalPages - 1) {
            gui.setItem(53, createItem(Material.ARROW, "§a§lNext Page", 
                "§7Go to page " + (page + 2), "", "§aClick to navigate"));
        }
        
        // Utility buttons
        gui.setItem(46, createItem(Material.COMPASS, "§b§l3D Preview Mode", 
            "§7Toggle 3D preview mode", "§7Shows ghost blocks before pasting", "", "§aClick to toggle"));
        
        gui.setItem(47, createItem(Material.REDSTONE_TORCH, "§c§lRefresh Library", 
            "§7Scan for new schematic files", "", "§aClick to refresh"));
        
        gui.setItem(48, createItem(Material.WRITABLE_BOOK, "§6§lUpload Schematic", 
            "§7Upload a new schematic file", "", "§aClick to upload"));
        
        // Back and close
        gui.setItem(50, createItem(Material.BARRIER, "§7§lBack to Tools", 
            "§7Return to main tools menu", "", "§aClick to go back"));
        
        gui.setItem(52, createItem(Material.RED_STAINED_GLASS_PANE, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Create an item representing a schematic file.
     */
    private ItemStack createSchematicItem(SchematicFile schematic) {
        Material material = getSchematicMaterial(schematic);
        
        List<String> lore = new ArrayList<>();
        lore.add("§7File: §f" + schematic.getFileName());
        lore.add("§7Size: §e" + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getLength());
        lore.add("§7Blocks: §e" + schematic.getBlockCount());
        lore.add("§7Category: §e" + schematic.getCategory());
        lore.add("");
        
        if (schematic.getDescription() != null && !schematic.getDescription().isEmpty()) {
            lore.add("§7Description:");
            lore.add("§f" + schematic.getDescription());
            lore.add("");
        }
        
        lore.add("§aLeft-click to preview");
        lore.add("§eRight-click to paste");
        lore.add("§bShift-click for options");
        
        return createItem(material, "§e§l" + schematic.getName(), lore.toArray(new String[0]));
    }
    
    /**
     * Get appropriate material for schematic display.
     */
    private Material getSchematicMaterial(SchematicFile schematic) {
        String category = schematic.getCategory().toLowerCase();
        
        switch (category) {
            case "structures": return Material.STONE_BRICKS;
            case "rooms": return Material.GRASS_BLOCK;
            case "traps": return Material.REDSTONE_BLOCK;
            case "arenas": return Material.SPAWNER;
            case "treasure": return Material.CHEST;
            case "portals": return Material.NETHER_PORTAL;
            default: return Material.STRUCTURE_BLOCK;
        }
    }
    
    /**
     * Get schematics for a specific category.
     */
    private List<SchematicFile> getSchematicsForCategory(String category) {
        List<SchematicFile> allSchematics = plugin.getSchematicManager().getAllSchematics();
        
        if ("all".equals(category)) {
            return allSchematics;
        }
        
        return allSchematics.stream()
            .filter(schematic -> category.equals(schematic.getCategory()))
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        playSound(player, Sound.UI_BUTTON_CLICK);
        
        handleSchematicLibraryClick(player, event.getSlot(), clickedItem, 
            event.isShiftClick(), event.isRightClick());
    }
    
    /**
     * Handle clicks in the schematic library.
     */
    private void handleSchematicLibraryClick(Player player, int slot, ItemStack item, boolean shift, boolean right) {
        // Category filters
        if (slot >= 1 && slot <= 7) {
            handleCategoryFilter(player, slot);
            return;
        }
        
        // Navigation
        if (slot == 45) { // Previous page
            changePage(player, -1);
            return;
        }
        if (slot == 53) { // Next page
            changePage(player, 1);
            return;
        }
        
        // Utility buttons
        switch (slot) {
            case 46: // 3D Preview Mode
                toggle3DPreview(player);
                break;
            case 47: // Refresh Library
                refreshLibrary(player);
                break;
            case 48: // Upload Schematic
                uploadSchematic(player);
                break;
            case 50: // Back to Tools
                new ToolsGUI(plugin).openToolsGUI(player);
                break;
            case 52: // Close
                player.closeInventory();
                break;
            default:
                // Handle schematic selection
                handleSchematicClick(player, slot, shift, right);
                break;
        }
    }
    
    /**
     * Handle category filter selection.
     */
    private void handleCategoryFilter(Player player, int slot) {
        String[] categories = {"all", "structures", "rooms", "traps", "arenas", "treasure", "portals"};
        String category = categories[slot - 1];
        
        currentCategory.put(player.getUniqueId(), category);
        currentPage.put(player.getUniqueId(), 0);
        
        // Refresh GUI
        Inventory newGui = createSchematicLibraryInventory(player);
        openGuis.put(player.getUniqueId(), newGui);
        player.openInventory(newGui);
        
        player.sendMessage("§aFiltered to category: §e" + category);
    }
    
    /**
     * Change page in the schematic library.
     */
    private void changePage(Player player, int direction) {
        int currentPageNum = currentPage.get(player.getUniqueId());
        int newPage = Math.max(0, currentPageNum + direction);
        
        List<SchematicFile> schematics = getSchematicsForCategory(currentCategory.get(player.getUniqueId()));
        int maxPage = (int) Math.ceil((double) schematics.size() / ITEMS_PER_PAGE) - 1;
        
        if (newPage <= maxPage) {
            currentPage.put(player.getUniqueId(), newPage);
            
            // Refresh GUI
            Inventory newGui = createSchematicLibraryInventory(player);
            openGuis.put(player.getUniqueId(), newGui);
            player.openInventory(newGui);
        }
    }
    
    /**
     * Handle schematic item clicks.
     */
    private void handleSchematicClick(Player player, int slot, boolean shift, boolean right) {
        SchematicFile schematic = getSchematicFromSlot(player, slot);
        if (schematic == null) return;
        
        if (shift) {
            openSchematicOptions(player, schematic);
        } else if (right) {
            pasteSchematic(player, schematic);
        } else {
            previewSchematic(player, schematic);
        }
    }
    
    /**
     * Get schematic from slot position.
     */
    private SchematicFile getSchematicFromSlot(Player player, int slot) {
        // Convert slot to schematic index
        if (slot < 10 || slot > 43) return null;
        
        int row = (slot - 10) / 9;
        int col = (slot - 10) % 9;
        
        if (col >= 7) return null; // Skip last column
        
        int index = row * 7 + col;
        int page = currentPage.get(player.getUniqueId());
        int schematicIndex = page * ITEMS_PER_PAGE + index;
        
        List<SchematicFile> schematics = getSchematicsForCategory(currentCategory.get(player.getUniqueId()));
        
        if (schematicIndex >= 0 && schematicIndex < schematics.size()) {
            return schematics.get(schematicIndex);
        }
        
        return null;
    }
    
    /**
     * Preview a schematic with 3D ghost blocks.
     */
    private void previewSchematic(Player player, SchematicFile schematic) {
        SchematicPreview preview = new SchematicPreview(plugin, schematic, player);
        preview.showPreview(player.getLocation());
        
        player.sendMessage("§aShowing 3D preview of: §e" + schematic.getName());
        player.sendMessage("§7Use WASD to move, Q/E to rotate, Enter to confirm, Escape to cancel");
        player.closeInventory();
    }
    
    /**
     * Paste a schematic directly.
     */
    private void pasteSchematic(Player player, SchematicFile schematic) {
        plugin.getSchematicManager().pasteSchematic(schematic, player.getLocation(), player);
        player.sendMessage("§aPasted schematic: §e" + schematic.getName());
        player.closeInventory();
    }
    
    /**
     * Open schematic options menu.
     */
    private void openSchematicOptions(Player player, SchematicFile schematic) {
        player.sendMessage("§eSchematic options for: §a" + schematic.getName());
        // This would open a sub-menu with options like delete, rename, export, etc.
    }
    
    /**
     * Toggle 3D preview mode.
     */
    private void toggle3DPreview(Player player) {
        // Toggle player's 3D preview preference
        player.sendMessage("§a3D Preview mode toggled!");
    }
    
    /**
     * Refresh the schematic library.
     */
    private void refreshLibrary(Player player) {
        plugin.getSchematicManager().refreshSchematics();
        
        // Refresh GUI
        Inventory newGui = createSchematicLibraryInventory(player);
        openGuis.put(player.getUniqueId(), newGui);
        player.openInventory(newGui);
        
        player.sendMessage("§aSchematic library refreshed!");
    }
    
    /**
     * Upload a new schematic.
     */
    private void uploadSchematic(Player player) {
        player.sendMessage("§eSchematic upload feature coming soon!");
        player.sendMessage("§7For now, place .schem files in the schematics folder and use refresh.");
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Play sound for player if enabled.
     */
    private void playSound(Player player, Sound sound) {
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), sound, 0.5f, 1.0f);
        }
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
            currentPage.remove(player.getUniqueId());
            currentCategory.remove(player.getUniqueId());
        }
    }
}
