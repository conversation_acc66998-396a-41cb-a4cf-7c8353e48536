package com.apexdungeons.core;

import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import java.util.List;
import java.util.ArrayList;

/**
 * Represents a boss definition with configuration for boss spawning and behavior
 */
public class BossDef {
    private String id;
    private String name;
    private String displayName;
    private Location location;
    private String mobType;
    private double healthMultiplier;
    private double damageMultiplier;
    private List<String> abilities;
    private List<String> lootTables;
    private boolean announceSpawn;
    private boolean announceDeath;
    private String spawnMessage;
    private String deathMessage;
    private boolean enabled;
    private String bossType; // "vanilla" or "mythic"
    
    public BossDef(String id) {
        this.id = id;
        this.name = id;
        this.displayName = id;
        this.mobType = "WITHER";
        this.healthMultiplier = 2.0;
        this.damageMultiplier = 1.5;
        this.abilities = new ArrayList<>();
        this.lootTables = new ArrayList<>();
        this.announceSpawn = true;
        this.announceDeath = true;
        this.spawnMessage = "&c&l[BOSS] &e{name} &chas awakened!";
        this.deathMessage = "&c&l[BOSS] &e{name} &chas been defeated!";
        this.enabled = true;
        this.bossType = "vanilla";
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }
    
    public String getMobType() { return mobType; }
    public void setMobType(String mobType) { this.mobType = mobType; }
    
    public double getHealthMultiplier() { return healthMultiplier; }
    public void setHealthMultiplier(double healthMultiplier) { this.healthMultiplier = healthMultiplier; }
    
    public double getDamageMultiplier() { return damageMultiplier; }
    public void setDamageMultiplier(double damageMultiplier) { this.damageMultiplier = damageMultiplier; }
    
    public List<String> getAbilities() { return abilities; }
    public void setAbilities(List<String> abilities) { this.abilities = abilities; }
    
    public List<String> getLootTables() { return lootTables; }
    public void setLootTables(List<String> lootTables) { this.lootTables = lootTables; }
    
    public boolean isAnnounceSpawn() { return announceSpawn; }
    public void setAnnounceSpawn(boolean announceSpawn) { this.announceSpawn = announceSpawn; }
    
    public boolean isAnnounceDeath() { return announceDeath; }
    public void setAnnounceDeath(boolean announceDeath) { this.announceDeath = announceDeath; }
    
    public String getSpawnMessage() { return spawnMessage; }
    public void setSpawnMessage(String spawnMessage) { this.spawnMessage = spawnMessage; }
    
    public String getDeathMessage() { return deathMessage; }
    public void setDeathMessage(String deathMessage) { this.deathMessage = deathMessage; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public String getBossType() { return bossType; }
    public void setBossType(String bossType) { this.bossType = bossType; }
    
    public void addAbility(String ability) {
        this.abilities.add(ability);
    }
    
    public void removeAbility(String ability) {
        this.abilities.remove(ability);
    }
    
    public void addLootTable(String lootTable) {
        this.lootTables.add(lootTable);
    }
    
    public void removeLootTable(String lootTable) {
        this.lootTables.remove(lootTable);
    }
}
