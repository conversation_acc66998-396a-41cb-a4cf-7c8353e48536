package com.apexdungeons.gui;

import com.apexdungeons.DungeonXMinimal;
import com.apexdungeons.core.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Advanced GUI for managing loot tables with full CRUD operations.
 * Supports creating, editing, and deleting loot tables and items.
 */
public class LootConfigGUI implements Listener {
    
    private final DungeonXMinimal plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    private final Map<UUID, String> editingTable = new HashMap<>();
    private final Map<UUID, String> chatInputMode = new HashMap<>();
    private final Set<UUID> awaitingInput = new HashSet<>();
    
    public LootConfigGUI(DungeonXMinimal plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the main loot configuration GUI.
     */
    public void openLootConfig(Player player) {
        if (!player.hasPermission("dungeonx.admin")) {
            player.sendMessage("§cYou don't have permission to configure loot tables.");
            return;
        }
        
        Inventory gui = createLootConfigInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create the main loot configuration inventory.
     */
    private Inventory createLootConfigInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lLoot Table Configuration");
        
        // Header
        gui.setItem(4, createItem(Material.CHEST, "§e§lLoot Table Manager", 
            "§7Manage all loot tables for dungeons", 
            "§7Create, edit, and delete loot tables", "", 
            "§aTotal Tables: §e" + getLootTables().size()));
        
        // Load existing loot tables
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection tables = lootConfig.getConfigurationSection("tables");
        
        int slot = 9;
        if (tables != null) {
            for (String tableName : tables.getKeys(false)) {
                if (slot >= 45) break; // Leave space for controls
                
                ConfigurationSection table = tables.getConfigurationSection(tableName);
                List<String> lore = new ArrayList<>();
                lore.add("§7Table: §e" + tableName);
                lore.add("§7Items: §a" + (table != null ? table.getKeys(false).size() : 0));
                lore.add("");
                lore.add("§eLeft-click: §7Edit table");
                lore.add("§eRight-click: §7Delete table");
                lore.add("§eShift-click: §7Duplicate table");
                
                gui.setItem(slot, createItem(Material.ENDER_CHEST, "§a§l" + tableName, lore.toArray(new String[0])));
                slot++;
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "§a§lCreate New Table", 
            "§7Create a new loot table", "", "§aClick to create"));
        
        gui.setItem(46, createItem(Material.BOOK, "§e§lImport Table", 
            "§7Import loot table from file", "", "§aClick to import"));
        
        gui.setItem(47, createItem(Material.WRITABLE_BOOK, "§6§lExport All", 
            "§7Export all tables to file", "", "§aClick to export"));
        
        gui.setItem(49, createItem(Material.REDSTONE, "§c§lReload Config", 
            "§7Reload loot configuration", "", "§aClick to reload"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Open table editor for specific loot table.
     */
    public void openTableEditor(Player player, String tableName) {
        editingTable.put(player.getUniqueId(), tableName);
        
        Inventory gui = createTableEditorInventory(player, tableName);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create table editor inventory.
     */
    private Inventory createTableEditorInventory(Player player, String tableName) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lEditing: " + tableName);
        
        // Header
        gui.setItem(4, createItem(Material.CHEST, "§e§lTable: " + tableName, 
            "§7Editing loot table", "", "§7Add, edit, or remove items"));
        
        // Load table items
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection table = lootConfig.getConfigurationSection("tables." + tableName);
        
        int slot = 9;
        if (table != null) {
            for (String key : table.getKeys(false)) {
                if (slot >= 45) break;
                
                ConfigurationSection item = table.getConfigurationSection(key);
                if (item != null) {
                    String itemType = item.getString("item", "minecraft:stone");
                    int amount = item.getInt("amount", 1);
                    int weight = item.getInt("weight", 1);
                    
                    Material material = parseMaterial(itemType);
                    
                    List<String> lore = new ArrayList<>();
                    lore.add("§7Item: §e" + itemType);
                    lore.add("§7Amount: §a" + amount);
                    lore.add("§7Weight: §b" + weight);
                    lore.add("");
                    lore.add("§eLeft-click: §7Edit item");
                    lore.add("§eRight-click: §7Delete item");
                    lore.add("§eShift-click: §7Duplicate item");
                    
                    gui.setItem(slot, createItem(material, "§a§lLoot Item #" + (slot - 8), lore.toArray(new String[0])));
                }
                slot++;
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "§a§lAdd Item", 
            "§7Add new item to table", "", "§aClick to add"));
        
        gui.setItem(46, createItem(Material.COMPARATOR, "§e§lTable Settings", 
            "§7Configure table settings", "", "§aClick to configure"));
        
        gui.setItem(47, createItem(Material.CHEST_MINECART, "§6§lTest Table", 
            "§7Generate test loot", "", "§aClick to test"));
        
        gui.setItem(49, createItem(Material.PAPER, "§b§lRename Table", 
            "§7Rename this table", "", "§aClick to rename"));
        
        gui.setItem(52, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to main menu", "", "§aClick to go back"));
        
        gui.setItem(53, createItem(Material.BARRIER, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        // Fill empty slots
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        
        return gui;
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        playSound(player, Sound.UI_BUTTON_CLICK);
        
        String title = event.getView().getTitle();
        if (title.equals("§6§lLoot Table Configuration")) {
            handleMainMenuClick(player, event.getSlot(), event.getClick());
        } else if (title.startsWith("§6§lEditing: ")) {
            handleTableEditorClick(player, event.getSlot(), event.getClick());
        }
    }
    
    /**
     * Handle main menu clicks.
     */
    private void handleMainMenuClick(Player player, int slot, ClickType clickType) {
        switch (slot) {
            case 45: // Create New Table
                startChatInput(player, "create_table", "§aEnter name for new loot table:");
                break;
            case 46: // Import Table
                player.sendMessage("§eImport feature coming soon!");
                break;
            case 47: // Export All
                exportAllTables(player);
                break;
            case 49: // Reload Config
                reloadLootConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Check if clicking on a table
                if (slot >= 9 && slot < 45) {
                    ItemStack item = player.getOpenInventory().getItem(slot);
                    if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                        String tableName = item.getItemMeta().getDisplayName().replace("§a§l", "");
                        
                        if (clickType == ClickType.LEFT) {
                            openTableEditor(player, tableName);
                        } else if (clickType == ClickType.RIGHT) {
                            deleteTable(player, tableName);
                        } else if (clickType == ClickType.SHIFT_LEFT) {
                            duplicateTable(player, tableName);
                        }
                    }
                }
                break;
        }
    }
    
    /**
     * Handle table editor clicks.
     */
    private void handleTableEditorClick(Player player, int slot, ClickType clickType) {
        String tableName = editingTable.get(player.getUniqueId());
        if (tableName == null) return;
        
        switch (slot) {
            case 45: // Add Item
                startItemCreation(player, tableName);
                break;
            case 46: // Table Settings
                openTableSettings(player, tableName);
                break;
            case 47: // Test Table
                testTable(player, tableName);
                break;
            case 49: // Rename Table
                startChatInput(player, "rename_table", "§aEnter new name for table:");
                break;
            case 52: // Back
                openLootConfig(player);
                break;
            case 53: // Close
                player.closeInventory();
                break;
            default:
                // Handle item clicks
                if (slot >= 9 && slot < 45) {
                    handleItemClick(player, slot, clickType, tableName);
                }
                break;
        }
    }
    
    /**
     * Handle item clicks in table editor.
     */
    private void handleItemClick(Player player, int slot, ClickType clickType, String tableName) {
        // Implementation for editing individual items
        if (clickType == ClickType.LEFT) {
            // Edit item
            startItemEdit(player, tableName, slot);
        } else if (clickType == ClickType.RIGHT) {
            // Delete item
            deleteItem(player, tableName, slot);
        } else if (clickType == ClickType.SHIFT_LEFT) {
            // Duplicate item
            duplicateItem(player, tableName, slot);
        }
    }
    
    /**
     * Start chat input for various operations.
     */
    private void startChatInput(Player player, String mode, String prompt) {
        chatInputMode.put(player.getUniqueId(), mode);
        awaitingInput.add(player.getUniqueId());
        player.closeInventory();
        player.sendMessage(prompt);
        player.sendMessage("§7Type 'cancel' to cancel operation.");
    }
    
    /**
     * Handle chat input for various operations.
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        if (!awaitingInput.contains(playerId)) return;
        
        event.setCancelled(true);
        awaitingInput.remove(playerId);
        
        String input = event.getMessage().trim();
        String mode = chatInputMode.remove(playerId);
        
        if (input.equalsIgnoreCase("cancel")) {
            player.sendMessage("§cOperation cancelled.");
            Bukkit.getScheduler().runTask(plugin, () -> openLootConfig(player));
            return;
        }
        
        Bukkit.getScheduler().runTask(plugin, () -> {
            switch (mode) {
                case "create_table":
                    createNewTable(player, input);
                    break;
                case "rename_table":
                    renameTable(player, input);
                    break;
                default:
                    player.sendMessage("§cUnknown input mode.");
                    break;
            }
        });
    }
    
    /**
     * Create a new loot table.
     */
    private void createNewTable(Player player, String tableName) {
        if (tableName.isEmpty() || !tableName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid table name. Use only letters, numbers, and underscores.");
            openLootConfig(player);
            return;
        }
        
        YamlConfiguration lootConfig = loadLootConfig();
        if (lootConfig.contains("tables." + tableName)) {
            player.sendMessage("§cTable '" + tableName + "' already exists!");
            openLootConfig(player);
            return;
        }
        
        // Create empty table
        lootConfig.createSection("tables." + tableName);
        saveLootConfig(lootConfig);
        
        player.sendMessage("§aCreated new loot table: " + tableName);
        openTableEditor(player, tableName);
    }
    
    /**
     * Delete a loot table.
     */
    private void deleteTable(Player player, String tableName) {
        YamlConfiguration lootConfig = loadLootConfig();
        lootConfig.set("tables." + tableName, null);
        saveLootConfig(lootConfig);
        
        player.sendMessage("§cDeleted loot table: " + tableName);
        openLootConfig(player);
    }
    
    /**
     * Duplicate a loot table.
     */
    private void duplicateTable(Player player, String tableName) {
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection originalTable = lootConfig.getConfigurationSection("tables." + tableName);
        
        if (originalTable == null) {
            player.sendMessage("§cTable not found!");
            return;
        }
        
        String newName = tableName + "_copy";
        int counter = 1;
        while (lootConfig.contains("tables." + newName)) {
            newName = tableName + "_copy" + counter;
            counter++;
        }
        
        // Copy the table
        ConfigurationSection newTable = lootConfig.createSection("tables." + newName);
        for (String key : originalTable.getKeys(true)) {
            newTable.set(key, originalTable.get(key));
        }
        
        saveLootConfig(lootConfig);
        player.sendMessage("§aDuplicated table as: " + newName);
        openLootConfig(player);
    }
    
    /**
     * Rename a loot table.
     */
    private void renameTable(Player player, String newName) {
        String oldName = editingTable.get(player.getUniqueId());
        if (oldName == null) return;
        
        if (newName.isEmpty() || !newName.matches("[a-zA-Z0-9_]+")) {
            player.sendMessage("§cInvalid table name. Use only letters, numbers, and underscores.");
            openTableEditor(player, oldName);
            return;
        }
        
        YamlConfiguration lootConfig = loadLootConfig();
        if (lootConfig.contains("tables." + newName)) {
            player.sendMessage("§cTable '" + newName + "' already exists!");
            openTableEditor(player, oldName);
            return;
        }
        
        // Copy old table to new name
        ConfigurationSection oldTable = lootConfig.getConfigurationSection("tables." + oldName);
        if (oldTable != null) {
            ConfigurationSection newTable = lootConfig.createSection("tables." + newName);
            for (String key : oldTable.getKeys(true)) {
                newTable.set(key, oldTable.get(key));
            }
            lootConfig.set("tables." + oldName, null);
            saveLootConfig(lootConfig);
            
            editingTable.put(player.getUniqueId(), newName);
            player.sendMessage("§aRenamed table to: " + newName);
            openTableEditor(player, newName);
        }
    }
    
    /**
     * Start item creation process.
     */
    private void startItemCreation(Player player, String tableName) {
        // This would open an item creation GUI
        player.sendMessage("§eItem creation GUI coming soon!");
        openTableEditor(player, tableName);
    }
    
    /**
     * Start item editing process.
     */
    private void startItemEdit(Player player, String tableName, int slot) {
        player.sendMessage("§eItem editing GUI coming soon!");
        openTableEditor(player, tableName);
    }
    
    /**
     * Delete an item from the table.
     */
    private void deleteItem(Player player, String tableName, int slot) {
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection table = lootConfig.getConfigurationSection("tables." + tableName);
        
        if (table != null) {
            List<String> keys = new ArrayList<>(table.getKeys(false));
            int itemIndex = slot - 9;
            if (itemIndex >= 0 && itemIndex < keys.size()) {
                String keyToDelete = keys.get(itemIndex);
                table.set(keyToDelete, null);
                saveLootConfig(lootConfig);
                player.sendMessage("§cDeleted item from table.");
            }
        }
        
        openTableEditor(player, tableName);
    }
    
    /**
     * Duplicate an item in the table.
     */
    private void duplicateItem(Player player, String tableName, int slot) {
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection table = lootConfig.getConfigurationSection("tables." + tableName);
        
        if (table != null) {
            List<String> keys = new ArrayList<>(table.getKeys(false));
            int itemIndex = slot - 9;
            if (itemIndex >= 0 && itemIndex < keys.size()) {
                String originalKey = keys.get(itemIndex);
                ConfigurationSection originalItem = table.getConfigurationSection(originalKey);
                
                if (originalItem != null) {
                    // Find a new key name
                    String newKey = originalKey + "_copy";
                    int counter = 1;
                    while (table.contains(newKey)) {
                        newKey = originalKey + "_copy" + counter;
                        counter++;
                    }
                    
                    // Copy the item
                    ConfigurationSection newItem = table.createSection(newKey);
                    for (String subKey : originalItem.getKeys(true)) {
                        newItem.set(subKey, originalItem.get(subKey));
                    }
                    
                    saveLootConfig(lootConfig);
                    player.sendMessage("§aDuplicated item in table.");
                }
            }
        }
        
        openTableEditor(player, tableName);
    }
    
    /**
     * Open table settings GUI.
     */
    private void openTableSettings(Player player, String tableName) {
        player.sendMessage("§eTable settings GUI coming soon!");
        openTableEditor(player, tableName);
    }
    
    /**
     * Test a loot table by generating sample loot.
     */
    private void testTable(Player player, String tableName) {
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection table = lootConfig.getConfigurationSection("tables." + tableName);
        
        if (table == null || table.getKeys(false).isEmpty()) {
            player.sendMessage("§cTable is empty or doesn't exist!");
            return;
        }
        
        player.sendMessage("§aGenerating test loot from table: " + tableName);
        
        // Generate 5 random items from the table
        for (int i = 0; i < 5; i++) {
            String randomItem = getRandomItemFromTable(table);
            if (randomItem != null) {
                player.sendMessage("§7- " + randomItem);
            }
        }
        
        openTableEditor(player, tableName);
    }
    
    /**
     * Get a random item from a loot table based on weights.
     */
    private String getRandomItemFromTable(ConfigurationSection table) {
        List<String> items = new ArrayList<>();
        List<Integer> weights = new ArrayList<>();
        int totalWeight = 0;
        
        for (String key : table.getKeys(false)) {
            ConfigurationSection item = table.getConfigurationSection(key);
            if (item != null) {
                String itemType = item.getString("item", "minecraft:stone");
                int amount = item.getInt("amount", 1);
                int weight = item.getInt("weight", 1);
                
                items.add(itemType + " x" + amount);
                weights.add(weight);
                totalWeight += weight;
            }
        }
        
        if (items.isEmpty()) return null;
        
        Random random = new Random();
        int randomWeight = random.nextInt(totalWeight);
        int currentWeight = 0;
        
        for (int i = 0; i < items.size(); i++) {
            currentWeight += weights.get(i);
            if (randomWeight < currentWeight) {
                return items.get(i);
            }
        }
        
        return items.get(0); // Fallback
    }
    
    /**
     * Export all loot tables to a file.
     */
    private void exportAllTables(Player player) {
        player.sendMessage("§eExport feature coming soon!");
    }
    
    /**
     * Reload loot configuration.
     */
    private void reloadLootConfig(Player player) {
        plugin.getConfigManager().loadConfigurations();
        player.sendMessage("§aLoot configuration reloaded!");
        openLootConfig(player);
    }
    
    /**
     * Load loot configuration.
     */
    private YamlConfiguration loadLootConfig() {
        File lootFile = new File(plugin.getDataFolder(), "loot.yml");
        if (!lootFile.exists()) {
            plugin.saveResource("loot.yml", false);
        }
        return YamlConfiguration.loadConfiguration(lootFile);
    }
    
    /**
     * Save loot configuration.
     */
    private void saveLootConfig(YamlConfiguration config) {
        try {
            File lootFile = new File(plugin.getDataFolder(), "loot.yml");
            config.save(lootFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save loot configuration: " + e.getMessage());
        }
    }
    
    /**
     * Get all loot table names.
     */
    private Set<String> getLootTables() {
        YamlConfiguration lootConfig = loadLootConfig();
        ConfigurationSection tables = lootConfig.getConfigurationSection("tables");
        return tables != null ? tables.getKeys(false) : new HashSet<>();
    }
    
    /**
     * Parse material from string.
     */
    private Material parseMaterial(String materialString) {
        try {
            // Remove minecraft: prefix if present
            String cleanName = materialString.replace("minecraft:", "").toUpperCase();
            Material material = Material.valueOf(cleanName);
            return material;
        } catch (IllegalArgumentException e) {
            return Material.STONE; // Default fallback
        }
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Play sound for player if enabled.
     */
    private void playSound(Player player, Sound sound) {
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
        }
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
        }
    }
    
    /**
     * Check if a player has a loot config GUI open.
     */
    public boolean hasLootConfigGUIOpen(Player player) {
        return openGuis.containsKey(player.getUniqueId());
    }
    
    /**
     * Close loot config GUI for a player.
     */
    public void closeLootConfigGUI(Player player) {
        if (openGuis.containsKey(player.getUniqueId())) {
            player.closeInventory();
        }
    }
}
