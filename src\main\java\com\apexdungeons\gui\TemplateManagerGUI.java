package com.apexdungeons.gui;

import com.apexdungeons.DungeonX;
import com.apexdungeons.core.DungeonTemplate;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Template Manager GUI for creating, editing, and managing dungeon templates.
 * Provides comprehensive template management with publishing workflow.
 */
public class TemplateManagerGUI implements Listener {
    
    private final DungeonX plugin;
    private final Map<UUID, Inventory> openGuis = new HashMap<>();
    private final Map<UUID, String> currentView = new HashMap<>(); // player -> view type
    
    public TemplateManagerGUI(DungeonX plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * Open the main template manager.
     */
    public void openTemplateManager(Player player) {
        currentView.put(player.getUniqueId(), "main");
        Inventory gui = createTemplateManagerInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Open published templates view.
     */
    public void openPublishedTemplates(Player player) {
        currentView.put(player.getUniqueId(), "published");
        Inventory gui = createPublishedTemplatesInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Open draft templates view.
     */
    public void openDraftTemplates(Player player) {
        currentView.put(player.getUniqueId(), "drafts");
        Inventory gui = createDraftTemplatesInventory(player);
        openGuis.put(player.getUniqueId(), gui);
        player.openInventory(gui);
        
        playSound(player, Sound.UI_BUTTON_CLICK);
    }
    
    /**
     * Create the main template manager inventory.
     */
    private Inventory createTemplateManagerInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lTemplate Manager");
        
        // Statistics
        int totalTemplates = plugin.getTemplateManager().getTemplateCount();
        int publishedCount = plugin.getTemplateManager().getPublishedTemplates().size();
        int draftCount = plugin.getTemplateManager().getUnpublishedTemplates().size();
        
        // Create New Template
        gui.setItem(10, createItem(Material.WRITABLE_BOOK, "§a§lCreate New Template", 
            "§7Create a new dungeon template", "§7from scratch", "", "§aClick to create"));
        
        // View All Templates
        gui.setItem(12, createItem(Material.BOOKSHELF, "§e§lAll Templates", 
            "§7View and manage all templates", "§7Total: §e" + totalTemplates, "", "§aClick to view"));
        
        // Published Templates
        gui.setItem(14, createItem(Material.EMERALD, "§a§lPublished Templates", 
            "§7Ready-to-play templates", "§7Published: §a" + publishedCount, "", "§aClick to view"));
        
        // Draft Templates
        gui.setItem(16, createItem(Material.PAPER, "§f§lDraft Templates", 
            "§7Work-in-progress templates", "§7Drafts: §f" + draftCount, "", "§aClick to view"));
        
        // Import/Export
        gui.setItem(28, createItem(Material.ENDER_CHEST, "§d§lImport Template", 
            "§7Import template from file", "", "§aClick to import"));
        
        gui.setItem(30, createItem(Material.SHULKER_BOX, "§d§lExport Template", 
            "§7Export template to file", "", "§aClick to export"));
        
        // Template Tools
        gui.setItem(32, createItem(Material.ANVIL, "§6§lTemplate Tools", 
            "§7Bulk operations and utilities", "", "§aClick to open"));
        
        // Recent Templates
        gui.setItem(37, createItem(Material.CLOCK, "§b§lRecent Templates", 
            "§7Recently modified templates", "", "§aClick to view"));
        
        // Back to Tools
        gui.setItem(45, createItem(Material.ARROW, "§7§lBack to Tools", 
            "§7Return to main tools menu", "", "§aClick to go back"));
        
        // Close
        gui.setItem(49, createItem(Material.RED_STAINED_GLASS_PANE, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        return gui;
    }
    
    /**
     * Create published templates inventory.
     */
    private Inventory createPublishedTemplatesInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§a§lPublished Templates");
        
        Collection<DungeonTemplate> publishedTemplates = plugin.getTemplateManager().getPublishedTemplates();
        List<DungeonTemplate> templateList = new ArrayList<>(publishedTemplates);
        
        // Add templates to GUI
        int slot = 9;
        for (int i = 0; i < Math.min(templateList.size(), 36); i++) {
            DungeonTemplate template = templateList.get(i);
            gui.setItem(slot, createTemplateItem(template, true));
            slot++;
            if (slot % 9 == 8) slot += 2; // Skip last column for navigation
        }
        
        // Navigation and controls
        gui.setItem(45, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to template manager", "", "§aClick to go back"));
        
        gui.setItem(49, createItem(Material.RED_STAINED_GLASS_PANE, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        return gui;
    }
    
    /**
     * Create draft templates inventory.
     */
    private Inventory createDraftTemplatesInventory(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§f§lDraft Templates");
        
        Collection<DungeonTemplate> draftTemplates = plugin.getTemplateManager().getUnpublishedTemplates();
        List<DungeonTemplate> templateList = new ArrayList<>(draftTemplates);
        
        // Add templates to GUI
        int slot = 9;
        for (int i = 0; i < Math.min(templateList.size(), 36); i++) {
            DungeonTemplate template = templateList.get(i);
            gui.setItem(slot, createTemplateItem(template, false));
            slot++;
            if (slot % 9 == 8) slot += 2; // Skip last column for navigation
        }
        
        // Navigation and controls
        gui.setItem(45, createItem(Material.ARROW, "§7§lBack", 
            "§7Return to template manager", "", "§aClick to go back"));
        
        gui.setItem(49, createItem(Material.RED_STAINED_GLASS_PANE, "§c§lClose", 
            "§7Close this menu", "", "§cClick to close"));
        
        fillEmptySlots(gui, Material.GRAY_STAINED_GLASS_PANE, " ");
        return gui;
    }
    
    /**
     * Create an item representing a template.
     */
    private ItemStack createTemplateItem(DungeonTemplate template, boolean published) {
        Material material = published ? Material.EMERALD : Material.PAPER;
        String status = published ? "§a§lPublished" : "§f§lDraft";
        
        List<String> lore = new ArrayList<>();
        lore.add("§7Creator: §e" + template.getCreator());
        lore.add("§7Spawners: §e" + template.getSpawners().size());
        lore.add("§7Bosses: §e" + template.getBosses().size());
        lore.add("§7Chests: §e" + template.getChests().size());
        lore.add("§7Status: " + status);
        lore.add("");
        
        if (!template.getDescription().isEmpty()) {
            lore.add("§7Description:");
            lore.add("§f" + template.getDescription());
            lore.add("");
        }
        
        lore.add("§aLeft-click to edit");
        lore.add("§eRight-click for options");
        if (!published) {
            lore.add("§bShift-click to publish");
        }
        
        return createItem(material, "§e§l" + template.getName(), lore.toArray(new String[0]));
    }
    
    /**
     * Handle inventory click events.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        
        if (!openGuis.containsKey(player.getUniqueId()) || 
            !openGuis.get(player.getUniqueId()).equals(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        playSound(player, Sound.UI_BUTTON_CLICK);
        
        String view = currentView.get(player.getUniqueId());
        if (view == null) view = "main";
        
        handleTemplateManagerClick(player, event.getSlot(), clickedItem, view, event.isShiftClick(), event.isRightClick());
    }
    
    /**
     * Handle clicks in template manager GUIs.
     */
    private void handleTemplateManagerClick(Player player, int slot, ItemStack item, String view, boolean shift, boolean right) {
        switch (view) {
            case "main":
                handleMainViewClick(player, slot, item);
                break;
            case "published":
                handlePublishedViewClick(player, slot, item, shift, right);
                break;
            case "drafts":
                handleDraftsViewClick(player, slot, item, shift, right);
                break;
        }
    }
    
    /**
     * Handle main view clicks.
     */
    private void handleMainViewClick(Player player, int slot, ItemStack item) {
        switch (slot) {
            case 10: // Create New Template
                createNewTemplate(player);
                break;
            case 12: // All Templates
                openTemplateManager(player);
                break;
            case 14: // Published Templates
                openPublishedTemplates(player);
                break;
            case 16: // Draft Templates
                openDraftTemplates(player);
                break;
            case 28: // Import Template
                player.sendMessage("§eTemplate import feature coming soon!");
                break;
            case 30: // Export Template
                player.sendMessage("§eTemplate export feature coming soon!");
                break;
            case 32: // Template Tools
                player.sendMessage("§eTemplate tools feature coming soon!");
                break;
            case 37: // Recent Templates
                player.sendMessage("§eRecent templates feature coming soon!");
                break;
            case 45: // Back to Tools
                new ToolsGUI(plugin).openToolsGUI(player);
                break;
            case 49: // Close
                player.closeInventory();
                break;
        }
    }
    
    /**
     * Handle published templates view clicks.
     */
    private void handlePublishedViewClick(Player player, int slot, ItemStack item, boolean shift, boolean right) {
        if (slot == 45) {
            openTemplateManager(player);
            return;
        }
        if (slot == 49) {
            player.closeInventory();
            return;
        }
        
        // Handle template clicks
        DungeonTemplate template = getTemplateFromSlot(slot, true);
        if (template != null) {
            if (right) {
                openTemplateOptions(player, template);
            } else {
                editTemplate(player, template);
            }
        }
    }
    
    /**
     * Handle drafts view clicks.
     */
    private void handleDraftsViewClick(Player player, int slot, ItemStack item, boolean shift, boolean right) {
        if (slot == 45) {
            openTemplateManager(player);
            return;
        }
        if (slot == 49) {
            player.closeInventory();
            return;
        }
        
        // Handle template clicks
        DungeonTemplate template = getTemplateFromSlot(slot, false);
        if (template != null) {
            if (shift) {
                publishTemplate(player, template);
            } else if (right) {
                openTemplateOptions(player, template);
            } else {
                editTemplate(player, template);
            }
        }
    }
    
    /**
     * Create a new template.
     */
    private void createNewTemplate(Player player) {
        String templateId = "template_" + System.currentTimeMillis();
        String templateName = "New Template";
        
        try {
            DungeonTemplate template = plugin.getTemplateManager().createTemplate(templateId, templateName, player.getName());
            player.sendMessage("§aCreated new template: §e" + templateName);
            player.sendMessage("§7Use the building tools to add spawners, bosses, and chests!");
            player.closeInventory();
        } catch (Exception e) {
            player.sendMessage("§cFailed to create template: " + e.getMessage());
        }
    }
    
    /**
     * Edit a template.
     */
    private void editTemplate(Player player, DungeonTemplate template) {
        player.sendMessage("§aEditing template: §e" + template.getName());
        player.sendMessage("§7Use the building tools to modify this template!");
        player.closeInventory();
    }
    
    /**
     * Publish a template.
     */
    private void publishTemplate(Player player, DungeonTemplate template) {
        if (plugin.getTemplateManager().publishTemplate(template.getId())) {
            player.sendMessage("§aPublished template: §e" + template.getName());
            openDraftTemplates(player); // Refresh view
        } else {
            player.sendMessage("§cFailed to publish template. Check for validation errors.");
        }
    }
    
    /**
     * Open template options menu.
     */
    private void openTemplateOptions(Player player, DungeonTemplate template) {
        player.sendMessage("§eTemplate options for: §a" + template.getName());
        // This would open a sub-menu with options like clone, delete, export, etc.
    }
    
    /**
     * Get template from slot position.
     */
    private DungeonTemplate getTemplateFromSlot(int slot, boolean published) {
        // This is a simplified implementation
        // In a real implementation, you'd track which template is in which slot
        Collection<DungeonTemplate> templates = published ? 
            plugin.getTemplateManager().getPublishedTemplates() : 
            plugin.getTemplateManager().getUnpublishedTemplates();
        
        List<DungeonTemplate> templateList = new ArrayList<>(templates);
        int index = slot - 9; // Adjust for starting position
        
        if (index >= 0 && index < templateList.size()) {
            return templateList.get(index);
        }
        
        return null;
    }
    
    /**
     * Create an item with name and lore.
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fill empty slots with glass panes.
     */
    private void fillEmptySlots(Inventory gui, Material material, String name) {
        ItemStack filler = createItem(material, name);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, filler);
            }
        }
    }
    
    /**
     * Play sound for player if enabled.
     */
    private void playSound(Player player, Sound sound) {
        if (plugin.getConfigManager().isGuiSoundsEnabled()) {
            player.playSound(player.getLocation(), sound, 0.5f, 1.0f);
        }
    }
    
    /**
     * Handle inventory close events.
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGuis.remove(player.getUniqueId());
            currentView.remove(player.getUniqueId());
        }
    }
}
