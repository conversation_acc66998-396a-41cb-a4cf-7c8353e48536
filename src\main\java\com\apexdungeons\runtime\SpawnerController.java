package com.apexdungeons.runtime;

import com.apexdungeons.DungeonX;
import com.apexdungeons.core.DungeonInstance;
import com.apexdungeons.core.SpawnerDef;
import com.apexdungeons.integration.MobAdapter;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Controls spawner behavior and mob spawning in dungeon instances.
 * Handles ONCE, RESPAWN, and WAVE policies with proper tracking.
 */
public class SpawnerController {
    
    private final DungeonX plugin;
    private final DungeonInstance instance;
    private final Map<String, Set<UUID>> spawnedMobs = new ConcurrentHashMap<>(); // spawnerId -> mob UUIDs
    private final Map<String, BukkitRunnable> spawnerTasks = new ConcurrentHashMap<>();
    
    private boolean active = false;
    
    public SpawnerController(DungeonX plugin, DungeonInstance instance) {
        this.plugin = plugin;
        this.instance = instance;
    }
    
    /**
     * Start the spawner controller.
     */
    public void start() {
        if (active) return;
        
        active = true;
        
        // Start spawner tick task (runs every second)
        BukkitRunnable mainTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!active || !instance.isActive()) {
                    cancel();
                    return;
                }
                tickSpawners();
            }
        };
        mainTask.runTaskTimer(plugin, 20L, 20L); // Every second
        
        plugin.getLogger().info("Started spawner controller for instance: " + instance.getInstanceId());
    }
    
    /**
     * Stop the spawner controller.
     */
    public void stop() {
        active = false;
        
        // Cancel all spawner tasks
        for (BukkitRunnable task : spawnerTasks.values()) {
            task.cancel();
        }
        spawnerTasks.clear();
        
        // Clear mob tracking
        spawnedMobs.clear();
        
        plugin.getLogger().info("Stopped spawner controller for instance: " + instance.getInstanceId());
    }
    
    /**
     * Tick all spawners to check for spawn conditions.
     */
    private void tickSpawners() {
        for (SpawnerDef spawner : instance.getActiveSpawners()) {
            tickSpawner(spawner);
        }
    }
    
    /**
     * Tick a specific spawner.
     */
    private void tickSpawner(SpawnerDef spawner) {
        // Clean up dead mobs first
        cleanupDeadMobs(spawner);
        
        // Check if spawner can spawn
        if (!spawner.canSpawn()) {
            return;
        }
        
        // Check trigger conditions
        if (!checkTriggerConditions(spawner)) {
            return;
        }
        
        // Spawn mobs
        spawnMobs(spawner);
    }
    
    /**
     * Clean up dead mobs for a spawner.
     */
    private void cleanupDeadMobs(SpawnerDef spawner) {
        Set<UUID> mobIds = spawnedMobs.get(spawner.getId());
        if (mobIds == null) return;
        
        Iterator<UUID> iterator = mobIds.iterator();
        while (iterator.hasNext()) {
            UUID mobId = iterator.next();
            LivingEntity entity = findEntityById(mobId);
            
            if (entity == null || entity.isDead()) {
                iterator.remove();
                spawner.recordDeath();
                instance.recordMobKill();
            }
        }
    }
    
    /**
     * Find an entity by UUID in the instance world.
     */
    private LivingEntity findEntityById(UUID entityId) {
        return instance.getWorld().getEntities().stream()
            .filter(entity -> entity.getUniqueId().equals(entityId))
            .filter(entity -> entity instanceof LivingEntity)
            .map(entity -> (LivingEntity) entity)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Check trigger conditions for a spawner.
     */
    private boolean checkTriggerConditions(SpawnerDef spawner) {
        switch (spawner.getTrigger()) {
            case ON_ENTER:
                return checkPlayerProximity(spawner);
            case ON_TIMER:
                return true; // Timer is handled by canSpawn()
            case ON_KILL:
                return checkPreviousMobsKilled(spawner);
            default:
                return false;
        }
    }
    
    /**
     * Check if players are within trigger radius.
     */
    private boolean checkPlayerProximity(SpawnerDef spawner) {
        Location spawnerLoc = spawner.getLocation();
        double triggerRadius = spawner.getTriggerRadius();
        
        for (Player player : instance.getPlayers()) {
            if (player.getWorld().equals(instance.getWorld()) &&
                player.getLocation().distance(spawnerLoc) <= triggerRadius) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if previous mobs from this spawner are killed.
     */
    private boolean checkPreviousMobsKilled(SpawnerDef spawner) {
        Set<UUID> mobIds = spawnedMobs.get(spawner.getId());
        return mobIds == null || mobIds.isEmpty();
    }
    
    /**
     * Spawn mobs for a spawner.
     */
    private void spawnMobs(SpawnerDef spawner) {
        int mobsToSpawn = spawner.getMobsToSpawn();
        if (mobsToSpawn <= 0) return;
        
        Location spawnLoc = spawner.getLocation();
        MobAdapter mobAdapter = plugin.getMobAdapter();
        
        if (mobAdapter == null) {
            plugin.getLogger().warning("No mob adapter available for spawning");
            return;
        }
        
        int successfulSpawns = 0;
        Set<UUID> spawnedMobIds = spawnedMobs.computeIfAbsent(spawner.getId(), k -> new HashSet<>());
        
        for (int i = 0; i < mobsToSpawn; i++) {
            // Calculate spawn location with some randomization
            Location randomLoc = getRandomSpawnLocation(spawnLoc, 2.0);
            
            try {
                LivingEntity entity;
                if (spawner.isMythic()) {
                    entity = mobAdapter.spawnMob(spawner.getMobType(), randomLoc);
                } else {
                    entity = mobAdapter.spawnMob(spawner.getMobType(), randomLoc);
                }
                
                if (entity != null) {
                    // Configure mob
                    configureMob(entity, spawner);
                    
                    // Track spawned mob
                    spawnedMobIds.add(entity.getUniqueId());
                    successfulSpawns++;
                    
                    // Apply aggro delay if configured
                    if (spawner.getAggroDelay() > 0) {
                        applyAggroDelay(entity, spawner.getAggroDelay());
                    }
                    
                    // Show spawn effects
                    showSpawnEffects(randomLoc);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to spawn mob " + spawner.getMobType() + ": " + e.getMessage());
            }
        }
        
        if (successfulSpawns > 0) {
            // Record spawn
            spawner.recordSpawn(successfulSpawns);
            
            plugin.getLogger().fine("Spawned " + successfulSpawns + " mobs for spawner " + spawner.getId());
        }
    }
    
    /**
     * Get a random spawn location around the spawner.
     */
    private Location getRandomSpawnLocation(Location center, double radius) {
        Random random = new Random();
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = random.nextDouble() * radius;
        
        double x = center.getX() + Math.cos(angle) * distance;
        double z = center.getZ() + Math.sin(angle) * distance;
        
        return new Location(center.getWorld(), x, center.getY(), z);
    }
    
    /**
     * Configure a spawned mob.
     */
    private void configureMob(LivingEntity entity, SpawnerDef spawner) {
        // Set custom name if needed
        if (spawner.getLevel() > 1) {
            entity.setCustomName("§e" + spawner.getMobType() + " §7(Level " + spawner.getLevel() + ")");
            entity.setCustomNameVisible(true);
        }
        
        // Apply level scaling (simplified)
        if (spawner.getLevel() > 1) {
            double healthMultiplier = 1.0 + (spawner.getLevel() - 1) * 0.5;
            entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH)
                .setBaseValue(entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getBaseValue() * healthMultiplier);
            entity.setHealth(entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getBaseValue());
        }
        
        // Set leash radius (simplified - would need custom AI in full implementation)
        entity.setPersistent(true);
    }
    
    /**
     * Apply aggro delay to a mob.
     */
    private void applyAggroDelay(LivingEntity entity, int delaySeconds) {
        // Make mob passive temporarily
        entity.setAI(false);
        
        // Re-enable AI after delay
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!entity.isDead()) {
                    entity.setAI(true);
                }
            }
        }.runTaskLater(plugin, delaySeconds * 20L);
    }
    
    /**
     * Show spawn effects.
     */
    private void showSpawnEffects(Location location) {
        if (plugin.getConfigManager().areParticlesEnabled()) {
            location.getWorld().spawnParticle(Particle.EXPLOSION, location, 1);
            location.getWorld().spawnParticle(Particle.SMOKE, location, 5, 0.5, 0.5, 0.5, 0.1);
        }
        
        if (plugin.getConfigManager().areSoundsEnabled()) {
            location.getWorld().playSound(location, Sound.ENTITY_ZOMBIE_VILLAGER_CONVERTED, 1.0f, 0.8f);
        }
    }
    
    /**
     * Force spawn a specific spawner (for testing/admin commands).
     */
    public void forceSpawn(String spawnerId) {
        SpawnerDef spawner = instance.getSpawner(spawnerId);
        if (spawner != null) {
            spawnMobs(spawner);
        }
    }
    
    /**
     * Get spawner statistics.
     */
    public Map<String, Object> getSpawnerStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("active", active);
        stats.put("totalSpawners", instance.getActiveSpawners().size());
        
        int totalMobs = 0;
        for (Set<UUID> mobs : spawnedMobs.values()) {
            totalMobs += mobs.size();
        }
        stats.put("totalAliveMobs", totalMobs);
        
        return stats;
    }
    
    /**
     * Check if the controller is active.
     */
    public boolean isActive() {
        return active;
    }
}
